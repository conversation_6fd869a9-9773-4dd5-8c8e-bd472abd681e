#!/bin/bash
# UglyAgent 安装脚本

set -e

echo "🤖 UglyAgent 安装脚本"
echo "======================"

# 检查Python版本
check_python() {
    echo "检查Python版本..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo "❌ 错误: 未找到Python。请安装Python 3.7或更高版本。"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 7 ]); then
        echo "❌ 错误: Python版本过低 ($PYTHON_VERSION)。需要Python 3.7或更高版本。"
        exit 1
    fi
    
    echo "✅ Python版本: $PYTHON_VERSION"
}

# 检查pip
check_pip() {
    echo "检查pip..."
    
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        echo "❌ 错误: 未找到pip。请安装pip。"
        exit 1
    fi
    
    echo "✅ pip已安装"
}

# 创建虚拟环境
create_venv() {
    echo "创建虚拟环境..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        echo "✅ 虚拟环境已创建"
    else
        echo "✅ 虚拟环境已存在"
    fi
}

# 激活虚拟环境
activate_venv() {
    echo "激活虚拟环境..."
    
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        echo "✅ 虚拟环境已激活"
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
        echo "✅ 虚拟环境已激活"
    else
        echo "❌ 错误: 无法找到虚拟环境激活脚本"
        exit 1
    fi
}

# 升级pip
upgrade_pip() {
    echo "升级pip..."
    pip install --upgrade pip
    echo "✅ pip已升级"
}

# 安装依赖
install_dependencies() {
    echo "安装依赖包..."
    
    # 安装核心依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        echo "✅ 核心依赖已安装"
    else
        echo "❌ 错误: 未找到requirements.txt文件"
        exit 1
    fi
    
    # 询问是否安装可选依赖
    echo ""
    echo "是否安装可选依赖？"
    echo "1) 完整安装 (包含所有功能)"
    echo "2) API服务器 (FastAPI + Uvicorn)"
    echo "3) 机器学习 (pandas, numpy, scikit-learn)"
    echo "4) Web工具 (selenium, lxml)"
    echo "5) 开发工具 (pytest, mypy, flake8)"
    echo "6) 跳过可选依赖"
    
    read -p "请选择 (1-6): " choice
    
    case $choice in
        1)
            pip install -e ".[full]"
            echo "✅ 完整依赖已安装"
            ;;
        2)
            pip install -e ".[api]"
            echo "✅ API服务器依赖已安装"
            ;;
        3)
            pip install -e ".[ml]"
            echo "✅ 机器学习依赖已安装"
            ;;
        4)
            pip install -e ".[web]"
            echo "✅ Web工具依赖已安装"
            ;;
        5)
            pip install -e ".[dev]"
            echo "✅ 开发工具依赖已安装"
            ;;
        6)
            echo "⏭️ 跳过可选依赖"
            ;;
        *)
            echo "⚠️ 无效选择，跳过可选依赖"
            ;;
    esac
}

# 创建配置文件
create_config() {
    echo "创建配置文件..."
    
    # 创建必要的目录
    mkdir -p config
    mkdir -p logs
    mkdir -p plugins
    mkdir -p backups
    
    # 复制配置文件模板
    if [ -f "config/config.yaml.example" ] && [ ! -f "config/config.yaml" ]; then
        cp config/config.yaml.example config/config.yaml
        echo "✅ 主配置文件已创建: config/config.yaml"
    fi
    
    if [ -f "config/models.yaml.example" ] && [ ! -f "config/models.yaml" ]; then
        cp config/models.yaml.example config/models.yaml
        echo "✅ 模型配置文件已创建: config/models.yaml"
        echo "⚠️ 请编辑 config/models.yaml 添加您的API密钥"
    fi
    
    echo "✅ 配置文件已创建"
}

# 设置权限
set_permissions() {
    echo "设置文件权限..."
    
    # 设置脚本可执行权限
    chmod +x scripts/*.sh 2>/dev/null || true
    chmod +x main.py 2>/dev/null || true
    
    echo "✅ 文件权限已设置"
}

# 运行测试
run_tests() {
    echo ""
    read -p "是否运行测试以验证安装？(y/N): " run_test
    
    if [[ $run_test =~ ^[Yy]$ ]]; then
        echo "运行测试..."
        
        if command -v pytest &> /dev/null; then
            pytest tests/ -v || echo "⚠️ 部分测试失败，但这不影响基本功能"
        else
            echo "运行基本测试..."
            $PYTHON_CMD -c "
import sys
sys.path.insert(0, '.')
try:
    from UglyAgent.core.agent import UglyAgent
    from UglyAgent.config.settings import get_config
    print('✅ 核心模块导入成功')
    
    config = get_config()
    print('✅ 配置系统正常')
    
    print('✅ 基本测试通过')
except Exception as e:
    print(f'❌ 测试失败: {e}')
    sys.exit(1)
"
        fi
        
        echo "✅ 测试完成"
    fi
}

# 显示安装完成信息
show_completion() {
    echo ""
    echo "🎉 UglyAgent 安装完成！"
    echo "========================"
    echo ""
    echo "下一步："
    echo "1. 编辑配置文件添加API密钥:"
    echo "   nano config/models.yaml"
    echo ""
    echo "2. 启动UglyAgent:"
    echo "   # 交互模式"
    echo "   python main.py"
    echo ""
    echo "   # MCP服务器模式"
    echo "   python main.py mcp-stdio"
    echo ""
    echo "   # API服务器模式"
    echo "   python main.py api"
    echo ""
    echo "3. 查看帮助:"
    echo "   python main.py --help"
    echo ""
    echo "4. 查看文档:"
    echo "   cat README.md"
    echo ""
    echo "享受使用UglyAgent！🚀"
}

# 主安装流程
main() {
    echo "开始安装UglyAgent..."
    echo ""
    
    check_python
    check_pip
    create_venv
    activate_venv
    upgrade_pip
    install_dependencies
    create_config
    set_permissions
    run_tests
    show_completion
}

# 错误处理
trap 'echo "❌ 安装过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
