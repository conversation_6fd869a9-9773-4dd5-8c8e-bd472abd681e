#!/usr/bin/env python3
"""
UglyAgent 工具基类
定义工具的标准接口和通用功能
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)


class ToolCategory(Enum):
    """工具分类"""
    FILE_OPERATIONS = "file_operations"
    CODE_ANALYSIS = "code_analysis"
    WEB_TOOLS = "web_tools"
    SYSTEM_TOOLS = "system_tools"
    AI_TOOLS = "ai_tools"
    DATA_PROCESSING = "data_processing"
    CUSTOM = "custom"


@dataclass
class ToolResult:
    """工具执行结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ToolDefinition:
    """工具定义"""
    name: str
    description: str
    category: ToolCategory
    parameters: Dict[str, Any]
    examples: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.examples is None:
            self.examples = []


class BaseTool(ABC):
    """工具基类"""
    
    def __init__(self, name: str, description: str, category: ToolCategory):
        self.name = name
        self.description = description
        self.category = category
        self.enabled = True
        self.usage_count = 0
        self.error_count = 0
        self.total_execution_time = 0.0
        self.last_used = None
        
        # 工具配置
        self.config = {}
        self.validators = []
        self.preprocessors = []
        self.postprocessors = []
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """执行工具"""
        pass
    
    @abstractmethod
    def get_parameters_schema(self) -> Dict[str, Any]:
        """获取参数模式"""
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """验证参数"""
        try:
            # 运行自定义验证器
            for validator in self.validators:
                if not validator(parameters):
                    return False
            
            # 基础验证
            schema = self.get_parameters_schema()
            required = schema.get("required", [])
            
            for param in required:
                if param not in parameters:
                    logger.error(f"缺少必需参数: {param}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"参数验证失败: {e}")
            return False
    
    def preprocess_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """预处理参数"""
        try:
            result = parameters.copy()
            
            for preprocessor in self.preprocessors:
                result = preprocessor(result)
            
            return result
            
        except Exception as e:
            logger.error(f"参数预处理失败: {e}")
            return parameters
    
    def postprocess_result(self, result: ToolResult) -> ToolResult:
        """后处理结果"""
        try:
            for postprocessor in self.postprocessors:
                result = postprocessor(result)
            
            return result
            
        except Exception as e:
            logger.error(f"结果后处理失败: {e}")
            return result
    
    async def run(self, **kwargs) -> ToolResult:
        """运行工具（包含完整的执行流程）"""
        start_time = time.time()
        
        try:
            # 检查工具是否启用
            if not self.enabled:
                return ToolResult(
                    success=False,
                    error=f"工具 {self.name} 已禁用"
                )
            
            # 验证参数
            if not self.validate_parameters(kwargs):
                return ToolResult(
                    success=False,
                    error="参数验证失败"
                )
            
            # 预处理参数
            processed_params = self.preprocess_parameters(kwargs)
            
            # 执行工具
            result = await self.execute(**processed_params)
            
            # 后处理结果
            result = self.postprocess_result(result)
            
            # 更新统计信息
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            self.usage_count += 1
            self.total_execution_time += execution_time
            self.last_used = time.time()
            
            if not result.success:
                self.error_count += 1
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.error_count += 1
            
            logger.error(f"工具 {self.name} 执行失败: {e}")
            
            return ToolResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def add_validator(self, validator: Callable[[Dict[str, Any]], bool]):
        """添加参数验证器"""
        self.validators.append(validator)
    
    def add_preprocessor(self, preprocessor: Callable[[Dict[str, Any]], Dict[str, Any]]):
        """添加参数预处理器"""
        self.preprocessors.append(preprocessor)
    
    def add_postprocessor(self, postprocessor: Callable[[ToolResult], ToolResult]):
        """添加结果后处理器"""
        self.postprocessors.append(postprocessor)
    
    def get_definition(self) -> ToolDefinition:
        """获取工具定义"""
        return ToolDefinition(
            name=self.name,
            description=self.description,
            category=self.category,
            parameters=self.get_parameters_schema()
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取工具统计信息"""
        avg_execution_time = (
            self.total_execution_time / self.usage_count 
            if self.usage_count > 0 else 0
        )
        
        error_rate = (
            self.error_count / self.usage_count 
            if self.usage_count > 0 else 0
        )
        
        return {
            "name": self.name,
            "category": self.category.value,
            "enabled": self.enabled,
            "usage_count": self.usage_count,
            "error_count": self.error_count,
            "error_rate": error_rate,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": avg_execution_time,
            "last_used": self.last_used
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.usage_count = 0
        self.error_count = 0
        self.total_execution_time = 0.0
        self.last_used = None
    
    def enable(self):
        """启用工具"""
        self.enabled = True
        logger.info(f"工具 {self.name} 已启用")
    
    def disable(self):
        """禁用工具"""
        self.enabled = False
        logger.info(f"工具 {self.name} 已禁用")
    
    def configure(self, **config):
        """配置工具"""
        self.config.update(config)
        logger.info(f"工具 {self.name} 配置已更新")


class ToolManager:
    """工具管理器"""
    
    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
        self.categories: Dict[ToolCategory, List[str]] = {}
        
        # 初始化分类
        for category in ToolCategory:
            self.categories[category] = []
    
    def register_tool(self, tool: BaseTool):
        """注册工具"""
        self.tools[tool.name] = tool
        self.categories[tool.category].append(tool.name)
        logger.info(f"已注册工具: {tool.name} ({tool.category.value})")
    
    def unregister_tool(self, tool_name: str):
        """注销工具"""
        if tool_name in self.tools:
            tool = self.tools[tool_name]
            del self.tools[tool_name]
            self.categories[tool.category].remove(tool_name)
            logger.info(f"已注销工具: {tool_name}")
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """获取工具"""
        return self.tools.get(tool_name)
    
    def get_tools_by_category(self, category: ToolCategory) -> List[BaseTool]:
        """按分类获取工具"""
        tool_names = self.categories.get(category, [])
        return [self.tools[name] for name in tool_names if name in self.tools]
    
    def list_tools(self) -> List[str]:
        """列出所有工具名称"""
        return list(self.tools.keys())
    
    def get_tool_definitions(self) -> List[ToolDefinition]:
        """获取所有工具定义"""
        return [tool.get_definition() for tool in self.tools.values()]
    
    async def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """执行工具"""
        tool = self.get_tool(tool_name)
        if not tool:
            return ToolResult(
                success=False,
                error=f"未找到工具: {tool_name}"
            )
        
        return await tool.run(**kwargs)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取所有工具的统计信息"""
        stats = {}
        for tool_name, tool in self.tools.items():
            stats[tool_name] = tool.get_statistics()
        
        return stats
    
    def enable_tool(self, tool_name: str):
        """启用工具"""
        tool = self.get_tool(tool_name)
        if tool:
            tool.enable()
    
    def disable_tool(self, tool_name: str):
        """禁用工具"""
        tool = self.get_tool(tool_name)
        if tool:
            tool.disable()
    
    def configure_tool(self, tool_name: str, **config):
        """配置工具"""
        tool = self.get_tool(tool_name)
        if tool:
            tool.configure(**config)


# 全局工具管理器实例
tool_manager = ToolManager()


def get_tool_manager() -> ToolManager:
    """获取全局工具管理器"""
    return tool_manager
