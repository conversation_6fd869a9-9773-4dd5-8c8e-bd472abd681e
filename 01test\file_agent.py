#!/usr/bin/env python3
"""
MCP File Agent - 一个使用MCP接口的文件操作代理
支持读取、修改和保存txt和py文件
"""

import json
import sys
import os
from typing import Dict, List, Any, Optional
from pathlib import Path


class MCPFileAgent:
    """MCP文件操作代理类"""
    
    def __init__(self):
        self.supported_extensions = {'.txt', '.py'}
        self.tools = {
            "read_file": {
                "description": "读取txt或py文件的内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要读取的文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            "modify_line": {
                "description": "修改文件中指定行的内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要修改的文件路径"
                        },
                        "line_number": {
                            "type": "integer",
                            "description": "要修改的行号（从1开始）"
                        },
                        "new_content": {
                            "type": "string",
                            "description": "新的行内容"
                        }
                    },
                    "required": ["file_path", "line_number", "new_content"]
                }
            },
            "save_file": {
                "description": "保存文件内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要保存的文件路径"
                        },
                        "content": {
                            "type": "string",
                            "description": "文件内容"
                        }
                    },
                    "required": ["file_path", "content"]
                }
            },
            "list_lines": {
                "description": "列出文件的所有行及其行号",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要列出行的文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    def _is_supported_file(self, file_path: str) -> bool:
        """检查文件扩展名是否支持"""
        return Path(file_path).suffix.lower() in self.supported_extensions
    
    def _validate_file_path(self, file_path: str) -> bool:
        """验证文件路径"""
        if not self._is_supported_file(file_path):
            return False
        return True
    
    def read_file(self, file_path: str) -> Dict[str, Any]:
        """读取文件内容"""
        try:
            if not self._validate_file_path(file_path):
                return {
                    "success": False,
                    "error": f"不支持的文件类型。仅支持: {', '.join(self.supported_extensions)}"
                }
            
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "success": True,
                "content": content,
                "file_path": file_path,
                "line_count": len(content.splitlines())
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"读取文件失败: {str(e)}"
            }
    
    def list_lines(self, file_path: str) -> Dict[str, Any]:
        """列出文件的所有行及其行号"""
        try:
            result = self.read_file(file_path)
            if not result["success"]:
                return result
            
            lines = result["content"].splitlines()
            numbered_lines = []
            
            for i, line in enumerate(lines, 1):
                numbered_lines.append({
                    "line_number": i,
                    "content": line
                })
            
            return {
                "success": True,
                "lines": numbered_lines,
                "total_lines": len(lines),
                "file_path": file_path
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"列出行失败: {str(e)}"
            }
    
    def modify_line(self, file_path: str, line_number: int, new_content: str) -> Dict[str, Any]:
        """修改文件中指定行的内容"""
        try:
            if not self._validate_file_path(file_path):
                return {
                    "success": False,
                    "error": f"不支持的文件类型。仅支持: {', '.join(self.supported_extensions)}"
                }
            
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 验证行号
            if line_number < 1 or line_number > len(lines):
                return {
                    "success": False,
                    "error": f"行号超出范围。文件共有 {len(lines)} 行，请输入1-{len(lines)}之间的行号"
                }
            
            # 保存原始内容用于返回
            original_content = lines[line_number - 1].rstrip('\n\r')
            
            # 修改指定行
            lines[line_number - 1] = new_content + '\n'
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            return {
                "success": True,
                "file_path": file_path,
                "line_number": line_number,
                "original_content": original_content,
                "new_content": new_content,
                "message": f"成功修改第 {line_number} 行"
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"修改行失败: {str(e)}"
            }
    
    def save_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """保存文件内容"""
        try:
            if not self._validate_file_path(file_path):
                return {
                    "success": False,
                    "error": f"不支持的文件类型。仅支持: {', '.join(self.supported_extensions)}"
                }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return {
                "success": True,
                "file_path": file_path,
                "message": f"文件保存成功: {file_path}",
                "size": len(content.encode('utf-8'))
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"保存文件失败: {str(e)}"
            }
    
    def handle_request(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP工具请求"""
        if tool_name not in self.tools:
            return {
                "success": False,
                "error": f"未知的工具: {tool_name}"
            }
        
        try:
            if tool_name == "read_file":
                return self.read_file(parameters["file_path"])
            elif tool_name == "modify_line":
                return self.modify_line(
                    parameters["file_path"],
                    parameters["line_number"],
                    parameters["new_content"]
                )
            elif tool_name == "save_file":
                return self.save_file(parameters["file_path"], parameters["content"])
            elif tool_name == "list_lines":
                return self.list_lines(parameters["file_path"])
            else:
                return {
                    "success": False,
                    "error": f"工具 {tool_name} 未实现"
                }
        
        except KeyError as e:
            return {
                "success": False,
                "error": f"缺少必需参数: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"处理请求时发生错误: {str(e)}"
            }
    
    def get_tools_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "tools": self.tools,
            "supported_extensions": list(self.supported_extensions)
        }


def main():
    """主函数 - 命令行接口"""
    agent = MCPFileAgent()
    
    if len(sys.argv) < 2:
        print("MCP File Agent - 文件操作代理")
        print("\n可用命令:")
        print("  python file_agent.py tools                    # 显示可用工具")
        print("  python file_agent.py read <file_path>         # 读取文件")
        print("  python file_agent.py list <file_path>         # 列出文件行")
        print("  python file_agent.py modify <file_path> <line_number> <new_content>  # 修改行")
        print("  python file_agent.py save <file_path> <content>  # 保存文件")
        return
    
    command = sys.argv[1]
    
    if command == "tools":
        tools_info = agent.get_tools_info()
        print(json.dumps(tools_info, indent=2, ensure_ascii=False))
    
    elif command == "read" and len(sys.argv) >= 3:
        result = agent.handle_request("read_file", {"file_path": sys.argv[2]})
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    elif command == "list" and len(sys.argv) >= 3:
        result = agent.handle_request("list_lines", {"file_path": sys.argv[2]})
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    elif command == "modify" and len(sys.argv) >= 5:
        result = agent.handle_request("modify_line", {
            "file_path": sys.argv[2],
            "line_number": int(sys.argv[3]),
            "new_content": sys.argv[4]
        })
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    elif command == "save" and len(sys.argv) >= 4:
        content = " ".join(sys.argv[3:])
        result = agent.handle_request("save_file", {
            "file_path": sys.argv[2],
            "content": content
        })
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    else:
        print("无效的命令或参数不足")


if __name__ == "__main__":
    main()
