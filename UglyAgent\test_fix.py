#!/usr/bin/env python3
"""
测试文件创建功能的修复
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append("./config/")
sys.path.append("./core/")
sys.path.append("./tools/")
sys.path.append("./utils/")

from core.agent import UglyAgent
from utils.logger import setup_logger

async def test_file_creation():
    """测试文件创建功能"""
    print("🧪 测试文件创建功能 - 详细过程显示")
    print("=" * 80)

    # 设置日志
    setup_logger(level=20)  # INFO级别，避免过多DEBUG信息

    try:
        async with UglyAgent() as agent:
            # 测试文件创建任务
            message = "写一个冒泡排序的python代码，包含详细注释，保存为test_bubble_sort_detailed.py"

            print(f"📝 用户请求: {message}")
            print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

            response = await agent.chat(
                message=message,
                use_memory=True,
                use_planning=True
            )

            print(f"\n" + "="*80)
            print(f"📊 最终结果")
            print(f"="*80)
            print(f"✅ 任务状态: {'成功' if response['success'] else '失败'}")
            print(f"📋 使用规划: {'是' if response.get('used_planning', False) else '否'}")
            print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

            if response['success']:
                print(f"\n📄 系统响应:")
                print(response['response'])

                # 检查文件是否真的被创建
                test_file = Path("test_bubble_sort_detailed.py")
                if test_file.exists():
                    print(f"\n✅ 文件验证:")
                    print(f"   📁 文件路径: {test_file.absolute()}")
                    print(f"   📏 文件大小: {test_file.stat().st_size} bytes")
                    print(f"   🕐 创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(test_file.stat().st_ctime))}")

                    # 显示文件内容的前几行
                    with open(test_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.splitlines()
                        print(f"\n📖 文件内容预览:")
                        print(f"   总行数: {len(lines)}")
                        print(f"   前15行内容:")
                        for i, line in enumerate(lines[:15], 1):
                            print(f"   {i:2d}: {line}")
                        if len(lines) > 15:
                            print(f"   ... (还有 {len(lines) - 15} 行)")

                    # 验证代码语法
                    try:
                        compile(content, test_file.name, 'exec')
                        print(f"   ✅ 语法检查: 通过")
                    except SyntaxError as e:
                        print(f"   ❌ 语法错误: {e}")

                else:
                    print("❌ 文件验证失败: 文件未被创建")
            else:
                print(f"❌ 任务失败: {response.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_file_creation())
