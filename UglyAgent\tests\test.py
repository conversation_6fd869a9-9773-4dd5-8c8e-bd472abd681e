```python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        swapped = False
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
                swapped = True
        if not swapped:
            break
    return arr

if __name__ == "__main__":
    test_array = [64, 34, 25, 12, 22, 11, 90]
    print("原始数组:", test_array)
    sorted_array = bubble_sort(test_array.copy())
    print("排序后数组:", sorted_array)
```

注：此代码已包含测试用例。若需要直接运行验证，可保存在指定路径并执行。排序算法已实现优化（当某次遍历没有交换时提前终止循环）。