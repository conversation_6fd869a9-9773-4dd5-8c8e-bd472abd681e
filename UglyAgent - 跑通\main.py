#!/usr/bin/env python3
"""
UglyAgent 主启动脚本
提供多种启动模式和配置选项
"""

import asyncio
import argparse
import sys
import os
import signal
from pathlib import Path
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append("./UglyAgent/")
sys.path.append("./UglyAgent/config/")
sys.path.append("./UglyAgent/core/")
sys.path.append("./UglyAgent/examples/")
sys.path.append("./UglyAgent/mcp/")
sys.path.append("./UglyAgent/plugins/")
sys.path.append("./UglyAgent/scripts/")
sys.path.append("./UglyAgent/tools/")
sys.path.append("./UglyAgent/utils/")


from core.agent import UglyAgent
from mcp.server import UglyAgentMCPServer
from utils.logger import setup_logger
from config.settings import get_config
from plugins.manager import get_plugin_manager


class UglyAgentLauncher:
    """UglyAgent启动器"""
    
    def __init__(self):
        self.agent: UglyAgent = None
        self.mcp_server: UglyAgentMCPServer = None
        self.plugin_manager = get_plugin_manager()
        self.running = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在关闭...")
        self.running = False
        asyncio.create_task(self.cleanup())
    
    async def start_interactive_mode(self):
        """启动交互模式"""
        print("🚀 UglyAgent 交互模式启动")
        print("输入 'help' 查看帮助，输入 'quit' 退出")
        print("-" * 50)
        
        self.agent = UglyAgent()
        await self.agent.start_session()
        
        # 加载插件
        await self.plugin_manager.discover_and_load_plugins()
        
        self.running = True
        
        while self.running:
            try:
                # user_input = input("\n👤 您: ").strip()
                user_input = "写一个冒泡排序的python，保存为D:/proj/27Augment/01MCP/UglyAgent/tests/test.py"
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 再见！")
                    break
                
                if user_input.lower() == 'help':
                    self._show_help()
                    continue
                
                if user_input.lower() == 'status':
                    await self._show_status()
                    continue
                
                if user_input.lower().startswith('plugin '):
                    await self._handle_plugin_command(user_input[7:])
                    continue
                
                # 处理用户消息
                print(f"🤖 UglyAgent收到任务：{user_input}")
                print("🤖 UglyAgent: 正在思考...")

                response = await self.agent.chat(
                    message=user_input,
                    use_memory=True,
                    use_planning=True
                )
                
                if response["success"]:
                    print(f"🤖 UglyAgent: {response['response']}")
                    
                    if response.get("used_planning"):
                        print(f"📋 使用了任务规划，执行了 {response.get('execution_result', {}).get('completed_tasks', 0)} 个任务")
                else:
                    print(f"❌ 错误: {response.get('error', '未知错误')}")

                self.running = False
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except EOFError:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

        await self.cleanup()
    
    async def start_mcp_server(self, mode: str = "stdio", host: str = "localhost", port: int = 8080):
        """启动MCP服务器模式"""
        print(f"🚀 UglyAgent MCP服务器启动 ({mode}模式)")
        
        self.mcp_server = UglyAgentMCPServer()
        
        # 加载插件
        await self.plugin_manager.discover_and_load_plugins()
        
        try:
            if mode == "stdio":
                await self.mcp_server.start_stdio_server()
            elif mode == "tcp":
                await self.mcp_server.start_tcp_server(host, port)
            else:
                raise ValueError(f"不支持的服务器模式: {mode}")
        
        except Exception as e:
            print(f"❌ MCP服务器启动失败: {e}")
        finally:
            await self.cleanup()
    
    async def start_api_server(self, host: str = "0.0.0.0", port: int = 8000):
        """启动API服务器模式"""
        try:
            from fastapi import FastAPI, HTTPException
            from fastapi.middleware.cors import CORSMiddleware
            from pydantic import BaseModel
            import uvicorn
            
            app = FastAPI(
                title="UglyAgent API",
                description="超越Augment的AI Agent API",
                version="1.0.0"
            )
            
            # 添加CORS中间件
            app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            # 初始化Agent
            self.agent = UglyAgent()
            await self.agent.start_session()
            
            # 加载插件
            await self.plugin_manager.discover_and_load_plugins()
            
            # API模型
            class ChatRequest(BaseModel):
                message: str
                model: str = None
                use_memory: bool = True
                use_planning: bool = False
            
            class TaskRequest(BaseModel):
                goal: str
                context: dict = {}
            
            # API端点
            @app.post("/chat")
            async def chat_endpoint(request: ChatRequest):
                try:
                    response = await self.agent.chat(
                        message=request.message,
                        model=request.model,
                        use_memory=request.use_memory,
                        use_planning=request.use_planning
                    )
                    return response
                except Exception as e:
                    raise HTTPException(status_code=500, detail=str(e))
            
            @app.post("/execute_task")
            async def execute_task_endpoint(request: TaskRequest):
                try:
                    response = await self.agent.execute_task(request.goal, request.context)
                    return response
                except Exception as e:
                    raise HTTPException(status_code=500, detail=str(e))
            
            @app.get("/status")
            async def status_endpoint():
                try:
                    return self.agent.get_agent_status()
                except Exception as e:
                    raise HTTPException(status_code=500, detail=str(e))
            
            @app.get("/plugins")
            async def plugins_endpoint():
                try:
                    return self.plugin_manager.get_plugin_status()
                except Exception as e:
                    raise HTTPException(status_code=500, detail=str(e))
            
            @app.get("/health")
            async def health_endpoint():
                return {"status": "healthy", "agent": "UglyAgent"}
            
            print(f"🌐 API服务器启动在 http://{host}:{port}")
            print(f"📖 API文档: http://{host}:{port}/docs")
            
            # 启动服务器
            config = uvicorn.Config(app, host=host, port=port, log_level="info")
            server = uvicorn.Server(config)
            await server.serve()
            
        except ImportError:
            print("❌ API模式需要安装 fastapi 和 uvicorn")
            print("请运行: pip install fastapi uvicorn")
        except Exception as e:
            print(f"❌ API服务器启动失败: {e}")
        finally:
            await self.cleanup()
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
🤖 UglyAgent 帮助信息

基本命令:
  help     - 显示此帮助信息
  status   - 显示Agent状态
  quit     - 退出程序

插件命令:
  plugin list              - 列出所有插件
  plugin activate <name>   - 激活插件
  plugin deactivate <name> - 停用插件
  plugin info <name>       - 显示插件信息

对话功能:
  - 直接输入消息与AI对话
  - 支持文件分析、代码审查、网页抓取等复杂任务
  - 自动任务规划和执行
  - 记忆管理和上下文理解

示例:
  分析这个Python文件: example.py
  爬取网页内容: https://example.com
  检查系统状态
  帮我写一个Python函数
        """
        print(help_text)
    
    async def _show_status(self):
        """显示状态信息"""
        try:
            agent_status = self.agent.get_agent_status()
            plugin_status = self.plugin_manager.get_plugin_status()
            
            print("\n📊 UglyAgent 状态:")
            print(f"  会话ID: {agent_status.get('session_id', 'N/A')}")
            print(f"  会话激活: {agent_status.get('session_active', False)}")
            print(f"  对话长度: {agent_status.get('conversation_length', 0)}")
            print(f"  可用模型: {len(agent_status.get('available_models', []))}")
            
            print(f"\n🔌 插件状态:")
            print(f"  总插件数: {plugin_status.get('total_plugins', 0)}")
            print(f"  已加载: {plugin_status.get('loaded_plugins', 0)}")
            print(f"  已激活: {plugin_status.get('active_plugins', 0)}")
            print(f"  可用工具: {plugin_status.get('registered_tools', 0)}")
            print(f"  可用命令: {plugin_status.get('registered_commands', 0)}")
            
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")
    
    async def _handle_plugin_command(self, command: str):
        """处理插件命令"""
        try:
            parts = command.strip().split()
            if not parts:
                print("❌ 插件命令格式错误")
                return
            
            action = parts[0]
            
            if action == "list":
                plugins = self.plugin_manager.get_plugin_status()["plugins"]
                print("\n🔌 插件列表:")
                for name, info in plugins.items():
                    status = "✅" if info["active"] else "⏸️" if info["loaded"] else "❌"
                    print(f"  {status} {name} - {info['info']['description']}")
            
            elif action == "activate" and len(parts) > 1:
                plugin_name = parts[1]
                success = await self.plugin_manager.activate_plugin(plugin_name)
                if success:
                    print(f"✅ 插件 {plugin_name} 已激活")
                else:
                    print(f"❌ 插件 {plugin_name} 激活失败")
            
            elif action == "deactivate" and len(parts) > 1:
                plugin_name = parts[1]
                success = await self.plugin_manager.deactivate_plugin(plugin_name)
                if success:
                    print(f"✅ 插件 {plugin_name} 已停用")
                else:
                    print(f"❌ 插件 {plugin_name} 停用失败")
            
            elif action == "info" and len(parts) > 1:
                plugin_name = parts[1]
                info = self.plugin_manager.get_plugin_info(plugin_name)
                if info:
                    print(f"\n📋 插件信息: {plugin_name}")
                    print(f"  版本: {info.version}")
                    print(f"  描述: {info.description}")
                    print(f"  作者: {info.author}")
                    print(f"  标签: {', '.join(info.tags)}")
                else:
                    print(f"❌ 未找到插件: {plugin_name}")
            
            else:
                print("❌ 未知的插件命令")
                print("可用命令: list, activate <name>, deactivate <name>, info <name>")
        
        except Exception as e:
            print(f"❌ 插件命令执行失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.agent:
                await self.agent.cleanup()
            
            if self.mcp_server:
                await self.mcp_server.stop()
            
            await self.plugin_manager.cleanup()
            
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"❌ 资源清理失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="UglyAgent - 超越Augment的AI Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
启动模式:
  interactive  - 交互式对话模式 (默认)
  mcp-stdio    - MCP协议标准输入输出模式
  mcp-tcp      - MCP协议TCP服务器模式
  api          - HTTP API服务器模式

示例:
  python main.py                          # 交互模式
  python main.py mcp-stdio                # MCP stdio模式
  python main.py mcp-tcp --port 8080      # MCP TCP模式
  python main.py api --host 0.0.0.0       # API服务器模式
        """
    )
    
    parser.add_argument(
        "mode",
        nargs="?",
        choices=["interactive", "mcp-stdio", "mcp-tcp", "api"],
        default="interactive",
        # default="api",
        help="启动模式"
    )
    
    parser.add_argument(
        "--host",
        default="localhost",
        help="服务器主机地址"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="服务器端口"
    )
    
    parser.add_argument(
        "--config",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    parser.add_argument(
        "--log-file",
        help="日志文件路径"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = getattr(logging, args.log_level)
    logger = setup_logger(
        level=log_level,
        log_file=args.log_file,
        console_output=True,
        colored_output=True
    )
    
    # 显示启动信息
    print("🤖 UglyAgent - 超越Augment的AI Agent")
    print("=" * 50)
    
    # 创建启动器
    launcher = UglyAgentLauncher()
    
    try:
        if args.mode == "interactive":
            await launcher.start_interactive_mode()
        elif args.mode == "mcp-stdio":
            await launcher.start_mcp_server("stdio")
        elif args.mode == "mcp-tcp":
            await launcher.start_mcp_server("tcp", args.host, args.port)
        elif args.mode == "api":
            await launcher.start_api_server(args.host, args.port)
    
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        print(f"❌ 启动失败: {e}")
    finally:
        await launcher.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
