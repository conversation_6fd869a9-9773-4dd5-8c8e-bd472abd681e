#!/usr/bin/env python3
"""
MCP File Server - 符合MCP协议标准的文件操作服务器
支持通过标准输入/输出进行JSON-RPC通信
"""

import json
import sys
import asyncio
from typing import Dict, List, Any, Optional
from file_agent import MCPFileAgent


class MCPServer:
    """MCP协议服务器"""
    
    def __init__(self):
        self.agent = MCPFileAgent()
        self.server_info = {
            "name": "file-agent-server",
            "version": "1.0.0",
            "description": "MCP文件操作服务器，支持读取、修改和保存txt和py文件"
        }
    
    def handle_initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        return {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "serverInfo": self.server_info
        }
    
    def handle_list_tools(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """列出可用工具"""
        tools = []
        for tool_name, tool_info in self.agent.tools.items():
            tools.append({
                "name": tool_name,
                "description": tool_info["description"],
                "inputSchema": tool_info["parameters"]
            })
        
        return {"tools": tools}
    
    def handle_call_tool(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if not tool_name:
            return {
                "content": [{
                    "type": "text",
                    "text": json.dumps({
                        "success": False,
                        "error": "工具名称不能为空"
                    }, ensure_ascii=False)
                }],
                "isError": True
            }
        
        # 调用agent处理请求
        result = self.agent.handle_request(tool_name, arguments)
        
        return {
            "content": [{
                "type": "text",
                "text": json.dumps(result, ensure_ascii=False, indent=2)
            }],
            "isError": not result.get("success", False)
        }
    
    def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")
        
        try:
            if method == "initialize":
                result = self.handle_initialize(params)
            elif method == "tools/list":
                result = self.handle_list_tools(params)
            elif method == "tools/call":
                result = self.handle_call_tool(params)
            else:
                result = {
                    "error": {
                        "code": -32601,
                        "message": f"未知方法: {method}"
                    }
                }
        
        except Exception as e:
            result = {
                "error": {
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            }
        
        response = {
            "jsonrpc": "2.0",
            "id": request_id
        }
        
        if "error" in result:
            response["error"] = result["error"]
        else:
            response["result"] = result
        
        return response
    
    async def run(self):
        """运行MCP服务器"""
        print("MCP File Server 启动中...", file=sys.stderr)
        print(f"服务器信息: {self.server_info}", file=sys.stderr)
        print("等待MCP客户端连接...", file=sys.stderr)
        
        try:
            while True:
                # 从标准输入读取请求
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )
                
                if not line:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    response = self.handle_request(request)
                    
                    # 发送响应到标准输出
                    print(json.dumps(response, ensure_ascii=False))
                    sys.stdout.flush()
                
                except json.JSONDecodeError as e:
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": f"JSON解析错误: {str(e)}"
                        }
                    }
                    print(json.dumps(error_response, ensure_ascii=False))
                    sys.stdout.flush()
        
        except KeyboardInterrupt:
            print("服务器关闭", file=sys.stderr)
        except Exception as e:
            print(f"服务器错误: {str(e)}", file=sys.stderr)


class MCPClient:
    """简单的MCP客户端用于测试"""
    
    def __init__(self):
        self.request_id = 0
    
    def get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def create_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建MCP请求"""
        request = {
            "jsonrpc": "2.0",
            "id": self.get_next_id(),
            "method": method
        }
        
        if params:
            request["params"] = params
        
        return request
    
    def test_server(self):
        """测试MCP服务器"""
        server = MCPServer()
        
        print("=== MCP服务器测试 ===")
        
        # 测试初始化
        init_request = self.create_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        })
        
        response = server.handle_request(init_request)
        print("初始化响应:")
        print(json.dumps(response, indent=2, ensure_ascii=False))
        
        # 测试列出工具
        list_tools_request = self.create_request("tools/list")
        response = server.handle_request(list_tools_request)
        print("\n工具列表:")
        print(json.dumps(response, indent=2, ensure_ascii=False))
        
        # 测试调用工具 - 保存文件
        save_request = self.create_request("tools/call", {
            "name": "save_file",
            "arguments": {
                "file_path": "mcp_test.py",
                "content": "# MCP测试文件\nprint('Hello from MCP!')\n"
            }
        })
        
        response = server.handle_request(save_request)
        print("\n保存文件响应:")
        print(json.dumps(response, indent=2, ensure_ascii=False))
        
        # 测试调用工具 - 读取文件
        read_request = self.create_request("tools/call", {
            "name": "read_file",
            "arguments": {
                "file_path": "mcp_test.py"
            }
        })
        
        response = server.handle_request(read_request)
        print("\n读取文件响应:")
        print(json.dumps(response, indent=2, ensure_ascii=False))
        
        # 测试调用工具 - 修改行
        modify_request = self.create_request("tools/call", {
            "name": "modify_line",
            "arguments": {
                "file_path": "mcp_test.py",
                "line_number": 2,
                "new_content": "print('Hello from Modified MCP!')"
            }
        })
        
        response = server.handle_request(modify_request)
        print("\n修改行响应:")
        print(json.dumps(response, indent=2, ensure_ascii=False))


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试模式
        client = MCPClient()
        client.test_server()
    else:
        # 服务器模式
        server = MCPServer()
        asyncio.run(server.run())


if __name__ == "__main__":
    main()
