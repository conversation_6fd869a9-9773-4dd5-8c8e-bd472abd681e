#!/usr/bin/env python3
"""
MCP File Agent 使用示例
演示如何使用文件操作代理
"""

from file_agent import MCPFileAgent
import json


def print_result(operation: str, result: dict):
    """打印操作结果"""
    print(f"\n=== {operation} ===")
    print(json.dumps(result, indent=2, ensure_ascii=False))


def main():
    """主演示函数"""
    # 创建agent实例
    agent = MCPFileAgent()
    
    print("MCP File Agent 使用演示")
    print("=" * 50)
    
    # 1. 显示可用工具
    tools_info = agent.get_tools_info()
    print_result("可用工具", tools_info)
    
    # 2. 创建一个测试文件
    test_content = """# 这是一个测试Python文件
def hello_world():
    print("Hello, World!")

def add_numbers(a, b):
    return a + b

if __name__ == "__main__":
    hello_world()
    result = add_numbers(5, 3)
    print(f"5 + 3 = {result}")
"""
    
    # 保存测试文件
    result = agent.handle_request("save_file", {
        "file_path": "test_file.py",
        "content": test_content
    })
    print_result("保存测试文件", result)
    
    # 3. 读取文件
    result = agent.handle_request("read_file", {
        "file_path": "test_file.py"
    })
    print_result("读取文件", result)
    
    # 4. 列出文件行
    result = agent.handle_request("list_lines", {
        "file_path": "test_file.py"
    })
    print_result("列出文件行", result)
    
    # 5. 修改第2行
    result = agent.handle_request("modify_line", {
        "file_path": "test_file.py",
        "line_number": 2,
        "new_content": "def hello_world(name='World'):"
    })
    print_result("修改第2行", result)
    
    # 6. 修改第3行
    result = agent.handle_request("modify_line", {
        "file_path": "test_file.py",
        "line_number": 3,
        "new_content": "    print(f\"Hello, {name}!\")"
    })
    print_result("修改第3行", result)
    
    # 7. 再次读取文件查看修改结果
    result = agent.handle_request("read_file", {
        "file_path": "test_file.py"
    })
    print_result("读取修改后的文件", result)
    
    # 8. 创建一个txt文件
    txt_content = """这是一个测试文本文件
第二行内容
第三行内容
第四行内容
"""
    
    result = agent.handle_request("save_file", {
        "file_path": "test_file.txt",
        "content": txt_content
    })
    print_result("保存txt文件", result)
    
    # 9. 修改txt文件的第2行
    result = agent.handle_request("modify_line", {
        "file_path": "test_file.txt",
        "line_number": 2,
        "new_content": "第二行内容已被修改"
    })
    print_result("修改txt文件第2行", result)
    
    # 10. 读取修改后的txt文件
    result = agent.handle_request("read_file", {
        "file_path": "test_file.txt"
    })
    print_result("读取修改后的txt文件", result)
    
    # 11. 测试错误情况
    result = agent.handle_request("read_file", {
        "file_path": "nonexistent.py"
    })
    print_result("读取不存在的文件", result)
    
    result = agent.handle_request("modify_line", {
        "file_path": "test_file.py",
        "line_number": 100,
        "new_content": "这行不存在"
    })
    print_result("修改不存在的行", result)
    
    result = agent.handle_request("read_file", {
        "file_path": "test.jpg"
    })
    print_result("读取不支持的文件类型", result)


if __name__ == "__main__":
    main()
