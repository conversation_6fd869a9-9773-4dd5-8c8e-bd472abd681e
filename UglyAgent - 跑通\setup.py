#!/usr/bin/env python3
"""
UglyAgent 安装脚本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() for line in f.readlines()
            if line.strip() and not line.startswith('#') and '# 内置模块' not in line
        ]
else:
    requirements = [
        "aiohttp>=3.8.0",
        "pydantic>=1.10.0",
        "PyYAML>=6.0",
        "beautifulsoup4>=4.11.0",
        "psutil>=5.9.0",
        "chardet>=5.0.0",
        "colorlog>=6.7.0",
        "requests>=2.28.0",
    ]

# 可选依赖
extras_require = {
    "full": [
        "openai>=1.0.0",
        "anthropic>=0.3.0",
        "fastapi>=0.95.0",
        "uvicorn>=0.20.0",
        "selenium>=4.0.0",
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "scikit-learn>=1.1.0",
    ],
    "api": [
        "fastapi>=0.95.0",
        "uvicorn>=0.20.0",
    ],
    "ml": [
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "scikit-learn>=1.1.0",
        "transformers>=4.20.0",
    ],
    "web": [
        "selenium>=4.0.0",
        "lxml>=4.9.0",
    ],
    "dev": [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "mypy>=1.0.0",
        "flake8>=5.0.0",
        "black>=22.0.0",
    ]
}

setup(
    name="uglyagent",
    version="1.0.0",
    author="UglyAgent Team",
    author_email="<EMAIL>",
    description="超越Augment的AI Agent - 使用MCP协议的强大AI助手",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/uglyagent/uglyagent",
    project_urls={
        "Bug Reports": "https://github.com/uglyagent/uglyagent/issues",
        "Source": "https://github.com/uglyagent/uglyagent",
        "Documentation": "https://uglyagent.readthedocs.io",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Distributed Computing",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    extras_require=extras_require,
    entry_points={
        "console_scripts": [
            "uglyagent=UglyAgent.main:main",
            "ugly-agent=UglyAgent.main:main",
            "uglyagent-mcp=UglyAgent.mcp.server:main",
        ],
    },
    include_package_data=True,
    package_data={
        "UglyAgent": [
            "config/*.yaml",
            "config/*.json",
            "plugins/examples/*.py",
            "plugins/examples/*.json",
            "docs/*.md",
        ],
    },
    zip_safe=False,
    keywords=[
        "ai", "agent", "mcp", "llm", "chatbot", "automation",
        "artificial intelligence", "machine learning", "nlp",
        "assistant", "augment", "claude", "openai", "gpt"
    ],
    license="MIT",
    platforms=["any"],
)
