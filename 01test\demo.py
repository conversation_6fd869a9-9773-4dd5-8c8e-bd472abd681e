#!/usr/bin/env python3
"""
MCP文件代理演示脚本
展示如何使用MCP文件代理的各种功能
"""

from file_agent import MCPFileAgent
import json


def print_separator(title):
    """打印分隔符"""
    print(f"\n{'='*50}")
    print(f" {title}")
    print('='*50)


def print_result(operation, result):
    """打印操作结果"""
    print(f"\n{operation}:")
    if result.get("success"):
        print("✅ 成功")
        # 只显示关键信息
        if "content" in result:
            content = result["content"]
            if len(content) > 200:
                content = content[:200] + "..."
            print(f"内容: {repr(content)}")
        if "message" in result:
            print(f"消息: {result['message']}")
        if "line_number" in result:
            print(f"行号: {result['line_number']}")
        if "original_content" in result:
            print(f"原内容: {repr(result['original_content'])}")
        if "new_content" in result:
            print(f"新内容: {repr(result['new_content'])}")
    else:
        print("❌ 失败")
        print(f"错误: {result.get('error', '未知错误')}")


def demo_basic_operations():
    """演示基本操作"""
    print_separator("基本文件操作演示")
    
    agent = MCPFileAgent()
    
    # 1. 创建一个Python文件
    python_content = '''#!/usr/bin/env python3
"""
演示用的Python文件
"""

def greet(name="World"):
    """问候函数"""
    return f"Hello, {name}!"

def calculate(a, b):
    """计算函数"""
    return a + b

if __name__ == "__main__":
    print(greet("MCP"))
    result = calculate(10, 20)
    print(f"10 + 20 = {result}")
'''
    
    result = agent.handle_request("save_file", {
        "file_path": "demo_example.py",
        "content": python_content
    })
    print_result("保存Python文件", result)
    
    # 2. 读取文件
    result = agent.handle_request("read_file", {
        "file_path": "demo_example.py"
    })
    print_result("读取Python文件", result)
    
    # 3. 列出文件行
    result = agent.handle_request("list_lines", {
        "file_path": "demo_example.py"
    })
    print_result("列出文件行", result)
    
    # 4. 修改第8行（greet函数的返回语句）
    result = agent.handle_request("modify_line", {
        "file_path": "demo_example.py",
        "line_number": 8,
        "new_content": '    return f"你好, {name}!"'
    })
    print_result("修改第8行", result)
    
    # 5. 修改第15行（添加更多功能）
    result = agent.handle_request("modify_line", {
        "file_path": "demo_example.py",
        "line_number": 15,
        "new_content": '    print(greet("MCP文件代理"))'
    })
    print_result("修改第15行", result)
    
    # 6. 再次读取文件查看修改结果
    result = agent.handle_request("read_file", {
        "file_path": "demo_example.py"
    })
    print_result("读取修改后的文件", result)


def demo_text_file_operations():
    """演示文本文件操作"""
    print_separator("文本文件操作演示")
    
    agent = MCPFileAgent()
    
    # 1. 创建文本文件
    text_content = """MCP文件代理使用说明

这是一个功能强大的文件操作工具，支持：
1. 读取txt和py文件
2. 修改文件中的指定行
3. 保存文件内容
4. 列出文件的所有行

使用方法简单，功能完善！
"""
    
    result = agent.handle_request("save_file", {
        "file_path": "demo_readme.txt",
        "content": text_content
    })
    print_result("保存文本文件", result)
    
    # 2. 列出行
    result = agent.handle_request("list_lines", {
        "file_path": "demo_readme.txt"
    })
    print_result("列出文本文件行", result)
    
    # 3. 修改标题
    result = agent.handle_request("modify_line", {
        "file_path": "demo_readme.txt",
        "line_number": 1,
        "new_content": "MCP文件代理 - 强大的文件操作工具"
    })
    print_result("修改标题", result)
    
    # 4. 添加新功能说明
    result = agent.handle_request("modify_line", {
        "file_path": "demo_readme.txt",
        "line_number": 7,
        "new_content": "5. 支持MCP协议标准接口"
    })
    print_result("添加新功能说明", result)


def demo_error_handling():
    """演示错误处理"""
    print_separator("错误处理演示")
    
    agent = MCPFileAgent()
    
    # 1. 尝试读取不存在的文件
    result = agent.handle_request("read_file", {
        "file_path": "not_exist.py"
    })
    print_result("读取不存在的文件", result)
    
    # 2. 尝试读取不支持的文件类型
    result = agent.handle_request("read_file", {
        "file_path": "image.jpg"
    })
    print_result("读取不支持的文件类型", result)
    
    # 3. 尝试修改超出范围的行
    result = agent.handle_request("modify_line", {
        "file_path": "demo_example.py",
        "line_number": 1000,
        "new_content": "这行不存在"
    })
    print_result("修改超出范围的行", result)


def demo_tools_info():
    """演示工具信息"""
    print_separator("工具信息演示")
    
    agent = MCPFileAgent()
    tools_info = agent.get_tools_info()
    
    print("可用工具:")
    for tool_name, tool_info in tools_info["tools"].items():
        print(f"\n🔧 {tool_name}")
        print(f"   描述: {tool_info['description']}")
        print(f"   参数: {list(tool_info['parameters']['properties'].keys())}")
    
    print(f"\n支持的文件类型: {', '.join(tools_info['supported_extensions'])}")


def main():
    """主演示函数"""
    print("🚀 MCP文件代理功能演示")
    print("这个演示将展示MCP文件代理的各种功能")
    
    try:
        # 运行各种演示
        demo_tools_info()
        demo_basic_operations()
        demo_text_file_operations()
        demo_error_handling()
        
        print_separator("演示完成")
        print("✅ 所有功能演示完成！")
        print("\n生成的演示文件:")
        print("- demo_example.py  (Python文件示例)")
        print("- demo_readme.txt  (文本文件示例)")
        print("\n你可以查看这些文件来了解修改效果。")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
