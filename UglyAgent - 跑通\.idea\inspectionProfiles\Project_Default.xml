<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="12">
            <item index="0" class="java.lang.String" itemvalue="tiktoken" />
            <item index="1" class="java.lang.String" itemvalue="langfuse" />
            <item index="2" class="java.lang.String" itemvalue="wheel" />
            <item index="3" class="java.lang.String" itemvalue="setuptools" />
            <item index="4" class="java.lang.String" itemvalue="bottle" />
            <item index="5" class="java.lang.String" itemvalue="requests" />
            <item index="6" class="java.lang.String" itemvalue="crewai" />
            <item index="7" class="java.lang.String" itemvalue="gymnasium" />
            <item index="8" class="java.lang.String" itemvalue="pillow" />
            <item index="9" class="java.lang.String" itemvalue="datasets" />
            <item index="10" class="java.lang.String" itemvalue="mcp" />
            <item index="11" class="java.lang.String" itemvalue="opencv-python" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>