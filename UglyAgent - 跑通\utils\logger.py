#!/usr/bin/env python3
"""
UglyAgent 日志系统
提供统一的日志配置和管理
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional
import colorlog


def setup_logger(name: str = "UglyAgent", 
                level: int = logging.INFO,
                log_file: Optional[str] = None,
                console_output: bool = True,
                colored_output: bool = True,
                max_file_size: int = 10 * 1024 * 1024,  # 10MB
                backup_count: int = 5) -> logging.Logger:
    """设置日志系统"""
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 日志格式
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 控制台输出
    if console_output:
        if colored_output and colorlog:
            # 彩色输出
            console_handler = colorlog.StreamHandler()
            console_handler.setFormatter(colorlog.ColoredFormatter(
                '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S',
                log_colors={
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                }
            ))
        else:
            # 普通输出
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(simple_formatter)
        
        console_handler.setLevel(level)
        logger.addHandler(console_handler)
    
    # 文件输出
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler进行日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(detailed_formatter)
        file_handler.setLevel(level)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """获取logger实例"""
    return logging.getLogger(name)


def set_log_level(level: int):
    """设置全局日志级别"""
    logging.getLogger().setLevel(level)
    
    # 更新所有处理器的级别
    for handler in logging.getLogger().handlers:
        handler.setLevel(level)


def configure_third_party_loggers():
    """配置第三方库的日志级别"""
    # 设置第三方库的日志级别，避免过多的调试信息
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)


# 默认配置
def setup_default_logger():
    """设置默认日志配置"""
    # 创建logs目录
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # 设置主logger
    logger = setup_logger(
        name="UglyAgent",
        level=logging.INFO,
        log_file="logs/uglyagent.log",
        console_output=True,
        colored_output=True
    )
    
    # 配置第三方库日志
    configure_third_party_loggers()
    
    return logger


if __name__ == "__main__":
    # 测试日志系统
    logger = setup_default_logger()
    
    logger.debug("这是一条调试信息")
    logger.info("这是一条信息")
    logger.warning("这是一条警告")
    logger.error("这是一条错误")
    logger.critical("这是一条严重错误")
