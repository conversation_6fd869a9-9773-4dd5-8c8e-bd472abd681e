```python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        swapped = False
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
                swapped = True
        if not swapped:
            break

def test_bubble_sort():
    import random
    
    # 测试空列表
    assert bubble_sort([]) == None
    
    # 测试单元素列表
    single = [5]
    bubble_sort(single)
    assert single == [5]
    
    # 测试已排序列表
    sorted_list = [1, 2, 3, 4, 5]
    bubble_sort(sorted_list)
    assert sorted_list == [1, 2, 3, 4, 5]
    
    # 测试逆序列表
    reverse_list = [5, 4, 3, 2, 1]
    bubble_sort(reverse_list)
    assert reverse_list == [1, 2, 3, 4, 5]
    
    # 测试随机列表
    random_list = [random.randint(0, 100) for _ in range(100)]
    expected = sorted(random_list)
    bubble_sort(random_list)
    assert random_list == expected

if __name__ == "__main__":
    test_bubble_sort()
    print("All tests passed!")
```

**注意：** 
1. 原始冒泡排序函数会原地修改列表，因此测试用例需要重新初始化数组
2. 添加了优化的交换标记(swapped)来提前终止排序
3. 包含完整的单元测试用例，覆盖边界条件和随机数据验证
4. 最终包含测试入口点，直接运行脚本会执行所有测试