#!/usr/bin/env python3
"""
演示UglyAgent详细过程显示的脚本
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))
sys.path.append("./config/")
sys.path.append("./core/")
sys.path.append("./tools/")
sys.path.append("./utils/")

from core.agent import UglyAgent
from utils.logger import setup_logger

def print_header(title: str):
    """打印标题"""
    print(f"\n{'='*80}")
    print(f"🎯 {title}")
    print(f"{'='*80}")

def print_section(title: str):
    """打印章节"""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

async def demo_detailed_process():
    """演示详细过程显示"""
    print_header("UglyAgent 详细过程演示")
    
    # 设置适当的日志级别
    setup_logger(level=20)  # INFO级别
    
    print(f"🕐 演示开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        async with UglyAgent() as agent:
            print_section("用户请求")
            
            # 演示任务
            task_message = "写一个计算斐波那契数列的Python函数，包含递归和迭代两种实现方式，保存为fibonacci_demo.py"
            
            print(f"📝 用户输入: {task_message}")
            print(f"🎯 任务类型: 代码生成 + 文件保存")
            print(f"🔍 预期结果: 创建包含斐波那契函数的Python文件")
            
            print_section("开始处理")
            
            # 执行任务
            response = await agent.chat(
                message=task_message,
                use_memory=True,
                use_planning=True
            )
            
            print_section("结果验证")
            
            if response['success']:
                print(f"✅ 任务执行成功!")
                print(f"📊 执行统计:")
                
                if 'execution_result' in response:
                    exec_result = response['execution_result']
                    print(f"   - 完成任务数: {exec_result.get('completed_tasks', 0)}")
                    print(f"   - 总任务数: {exec_result.get('total_tasks', 0)}")
                    print(f"   - 执行时间: {exec_result.get('execution_time', 0):.2f} 秒")
                
                # 验证文件创建
                target_file = Path("fibonacci_demo.py")
                if target_file.exists():
                    print(f"\n📁 文件验证:")
                    print(f"   ✅ 文件已创建: {target_file.name}")
                    print(f"   📏 文件大小: {target_file.stat().st_size} bytes")
                    
                    # 读取并显示文件内容
                    with open(target_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.splitlines()
                        
                    print(f"   📖 文件内容 ({len(lines)} 行):")
                    
                    # 显示前20行
                    for i, line in enumerate(lines[:20], 1):
                        print(f"   {i:2d}: {line}")
                    
                    if len(lines) > 20:
                        print(f"   ... (还有 {len(lines) - 20} 行)")
                    
                    # 语法检查
                    try:
                        compile(content, target_file.name, 'exec')
                        print(f"   ✅ Python语法: 正确")
                    except SyntaxError as e:
                        print(f"   ❌ 语法错误: 第{e.lineno}行 - {e.msg}")
                    
                    # 检查是否包含预期的函数
                    if 'fibonacci' in content.lower():
                        print(f"   ✅ 包含斐波那契函数")
                    if 'def ' in content:
                        func_count = content.count('def ')
                        print(f"   📊 函数数量: {func_count}")
                    
                else:
                    print(f"❌ 文件验证失败: 未找到 {target_file.name}")
                
                print(f"\n📄 系统响应:")
                print(response['response'])
                
            else:
                print(f"❌ 任务执行失败")
                print(f"🔍 错误信息: {response.get('error', '未知错误')}")
            
            print_section("演示总结")
            
            print(f"🎯 本次演示展示了UglyAgent的以下能力:")
            print(f"   1. 智能任务分析和规划")
            print(f"   2. 详细的执行过程显示")
            print(f"   3. AI模型交互的透明化")
            print(f"   4. 任务间依赖关系处理")
            print(f"   5. 实时进度反馈")
            print(f"   6. 结果验证和质量检查")
            
            print(f"\n🕐 演示结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def demo_simple_task():
    """演示简单任务的详细过程"""
    print_header("简单任务演示 - 数学计算")
    
    try:
        async with UglyAgent() as agent:
            # 简单的数学任务
            simple_task = "计算1到100的所有质数，并保存到primes.txt文件中"
            
            print(f"📝 任务: {simple_task}")
            
            response = await agent.chat(
                message=simple_task,
                use_planning=True
            )
            
            if response['success']:
                print(f"✅ 简单任务也能看到详细过程!")
            else:
                print(f"❌ 任务失败: {response.get('error')}")
                
    except Exception as e:
        print(f"❌ 简单任务演示失败: {e}")

async def main():
    """主函数"""
    print("🚀 UglyAgent 详细过程显示演示")
    print("本演示将展示优化后的详细信息显示功能")
    
    # 运行主要演示
    await demo_detailed_process()
    
    # 可选：运行简单任务演示
    print(f"\n" + "="*80)
    user_input = input("是否运行简单任务演示? (y/N): ").strip().lower()
    if user_input in ['y', 'yes']:
        await demo_simple_task()
    
    print(f"\n🎉 演示完成!")
    print(f"💡 现在UglyAgent会显示:")
    print(f"   - 任务分析过程")
    print(f"   - 计划创建详情")
    print(f"   - 每个任务的执行步骤")
    print(f"   - AI模型的交互内容")
    print(f"   - 实时进度更新")
    print(f"   - 详细的结果验证")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        print("请检查配置和依赖是否正确安装")
