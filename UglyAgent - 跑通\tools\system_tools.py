#!/usr/bin/env python3
"""
UglyAgent 系统工具
提供系统信息获取、命令执行、进程管理等功能
"""

import os
import sys
import subprocess
import psutil
import platform
import shutil
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
import asyncio

logger = logging.getLogger(__name__)


class SystemTools:
    """系统工具集合"""
    
    def __init__(self):
        self.safe_commands = {
            'ls', 'dir', 'pwd', 'cd', 'cat', 'type', 'echo', 'find', 'grep',
            'head', 'tail', 'wc', 'sort', 'uniq', 'cut', 'awk', 'sed',
            'ps', 'top', 'df', 'du', 'free', 'uptime', 'whoami', 'id',
            'date', 'cal', 'which', 'whereis', 'file', 'stat'
        }
        self.dangerous_commands = {
            'rm', 'del', 'rmdir', 'rd', 'format', 'fdisk', 'mkfs',
            'dd', 'shutdown', 'reboot', 'halt', 'poweroff', 'kill',
            'killall', 'pkill', 'chmod', 'chown', 'su', 'sudo'
        }
        self.max_execution_time = 60  # 最大执行时间(秒)
        self.max_output_size = 1024 * 1024  # 最大输出大小(1MB)
    
    async def execute_command(self, command: str, 
                             working_directory: str = None,
                             timeout: int = 30,
                             safe_mode: bool = True) -> Dict[str, Any]:
        """执行系统命令"""
        try:
            # 安全检查
            if safe_mode:
                cmd_parts = command.strip().split()
                if not cmd_parts:
                    return {
                        "success": False,
                        "error": "命令不能为空"
                    }
                
                base_cmd = cmd_parts[0].lower()
                
                # 检查危险命令
                if base_cmd in self.dangerous_commands:
                    return {
                        "success": False,
                        "error": f"危险命令被禁止: {base_cmd}. 使用 safe_mode=False 强制执行"
                    }
                
                # 检查路径遍历
                if '..' in command or '~' in command:
                    return {
                        "success": False,
                        "error": "命令包含潜在危险的路径"
                    }
            
            # 设置工作目录
            cwd = working_directory or os.getcwd()
            if not os.path.exists(cwd):
                return {
                    "success": False,
                    "error": f"工作目录不存在: {cwd}"
                }
            
            # 限制超时时间
            timeout = min(timeout, self.max_execution_time)
            
            # 执行命令
            start_time = time.time()
            
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                limit=self.max_output_size
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return {
                    "success": False,
                    "error": f"命令执行超时 ({timeout}秒)"
                }
            
            execution_time = time.time() - start_time
            
            # 解码输出
            stdout_text = stdout.decode('utf-8', errors='ignore') if stdout else ""
            stderr_text = stderr.decode('utf-8', errors='ignore') if stderr else ""
            
            return {
                "success": True,
                "command": command,
                "return_code": process.returncode,
                "stdout": stdout_text,
                "stderr": stderr_text,
                "execution_time": execution_time,
                "working_directory": cwd
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"命令执行失败: {str(e)}"
            }
    
    async def get_system_info(self, info_type: str = "all") -> Dict[str, Any]:
        """获取系统信息"""
        try:
            info = {}
            
            if info_type in ["all", "basic"]:
                info["basic"] = {
                    "platform": platform.platform(),
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor(),
                    "architecture": platform.architecture(),
                    "hostname": platform.node(),
                    "python_version": platform.python_version()
                }
            
            if info_type in ["all", "cpu"]:
                info["cpu"] = {
                    "physical_cores": psutil.cpu_count(logical=False),
                    "logical_cores": psutil.cpu_count(logical=True),
                    "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "cpu_times": psutil.cpu_times()._asdict(),
                    "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
                }
            
            if info_type in ["all", "memory"]:
                memory = psutil.virtual_memory()
                swap = psutil.swap_memory()
                
                info["memory"] = {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "free": memory.free,
                    "percent": memory.percent,
                    "swap_total": swap.total,
                    "swap_used": swap.used,
                    "swap_free": swap.free,
                    "swap_percent": swap.percent
                }
            
            if info_type in ["all", "disk"]:
                disk_usage = psutil.disk_usage('/')
                disk_partitions = [p._asdict() for p in psutil.disk_partitions()]
                
                info["disk"] = {
                    "usage": {
                        "total": disk_usage.total,
                        "used": disk_usage.used,
                        "free": disk_usage.free,
                        "percent": (disk_usage.used / disk_usage.total) * 100
                    },
                    "partitions": disk_partitions
                }
            
            if info_type in ["all", "network"]:
                network_stats = psutil.net_io_counters()
                network_interfaces = {}
                
                for interface, addrs in psutil.net_if_addrs().items():
                    network_interfaces[interface] = [addr._asdict() for addr in addrs]
                
                info["network"] = {
                    "stats": network_stats._asdict() if network_stats else None,
                    "interfaces": network_interfaces
                }
            
            if info_type in ["all", "processes"]:
                processes = []
                for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                    try:
                        processes.append(proc.info)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                # 按CPU使用率排序，取前10个
                processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
                info["processes"] = {
                    "count": len(processes),
                    "top_cpu": processes[:10]
                }
            
            return {
                "success": True,
                "info_type": info_type,
                "system_info": info,
                "timestamp": time.time()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"获取系统信息失败: {str(e)}"
            }
    
    async def get_process_info(self, pid: int = None, 
                             name: str = None) -> Dict[str, Any]:
        """获取进程信息"""
        try:
            processes = []
            
            if pid:
                # 获取指定PID的进程
                try:
                    proc = psutil.Process(pid)
                    processes.append(proc)
                except psutil.NoSuchProcess:
                    return {
                        "success": False,
                        "error": f"进程不存在: PID {pid}"
                    }
            elif name:
                # 按名称查找进程
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if name.lower() in proc.info['name'].lower():
                            processes.append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                if not processes:
                    return {
                        "success": False,
                        "error": f"未找到进程: {name}"
                    }
            else:
                return {
                    "success": False,
                    "error": "必须指定PID或进程名称"
                }
            
            # 获取进程详细信息
            process_info = []
            for proc in processes:
                try:
                    info = proc.as_dict(attrs=[
                        'pid', 'name', 'status', 'create_time', 'cpu_percent',
                        'memory_percent', 'memory_info', 'cmdline', 'cwd',
                        'username', 'num_threads', 'connections'
                    ])
                    
                    # 格式化创建时间
                    info['create_time_formatted'] = time.strftime(
                        '%Y-%m-%d %H:%M:%S', 
                        time.localtime(info['create_time'])
                    )
                    
                    process_info.append(info)
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    process_info.append({
                        "pid": proc.pid,
                        "error": str(e)
                    })
            
            return {
                "success": True,
                "processes": process_info,
                "count": len(process_info)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"获取进程信息失败: {str(e)}"
            }
    
    async def monitor_system(self, duration: int = 60, 
                           interval: int = 5) -> Dict[str, Any]:
        """监控系统资源使用情况"""
        try:
            if duration > 300:  # 最大5分钟
                duration = 300
            
            if interval < 1:
                interval = 1
            
            samples = []
            start_time = time.time()
            
            while time.time() - start_time < duration:
                sample = {
                    "timestamp": time.time(),
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else None,
                    "network_io": psutil.net_io_counters()._asdict() if psutil.net_io_counters() else None
                }
                
                samples.append(sample)
                
                if len(samples) * interval >= duration:
                    break
                
                await asyncio.sleep(interval)
            
            # 计算统计信息
            if samples:
                cpu_values = [s["cpu_percent"] for s in samples]
                memory_values = [s["memory_percent"] for s in samples]
                
                stats = {
                    "cpu": {
                        "avg": sum(cpu_values) / len(cpu_values),
                        "max": max(cpu_values),
                        "min": min(cpu_values)
                    },
                    "memory": {
                        "avg": sum(memory_values) / len(memory_values),
                        "max": max(memory_values),
                        "min": min(memory_values)
                    }
                }
            else:
                stats = {}
            
            return {
                "success": True,
                "duration": duration,
                "interval": interval,
                "samples": samples,
                "statistics": stats,
                "sample_count": len(samples)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"系统监控失败: {str(e)}"
            }
    
    async def get_environment_variables(self, pattern: str = None) -> Dict[str, Any]:
        """获取环境变量"""
        try:
            env_vars = dict(os.environ)
            
            if pattern:
                # 过滤环境变量
                import re
                regex = re.compile(pattern, re.IGNORECASE)
                filtered_vars = {
                    key: value for key, value in env_vars.items()
                    if regex.search(key)
                }
                env_vars = filtered_vars
            
            return {
                "success": True,
                "environment_variables": env_vars,
                "count": len(env_vars),
                "pattern": pattern
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"获取环境变量失败: {str(e)}"
            }
    
    async def check_disk_space(self, paths: List[str] = None) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            if not paths:
                paths = ['/'] if os.name != 'nt' else ['C:\\']
            
            disk_info = {}
            
            for path in paths:
                if not os.path.exists(path):
                    disk_info[path] = {"error": "路径不存在"}
                    continue
                
                try:
                    usage = shutil.disk_usage(path)
                    disk_info[path] = {
                        "total": usage.total,
                        "used": usage.used,
                        "free": usage.free,
                        "percent_used": (usage.used / usage.total) * 100,
                        "percent_free": (usage.free / usage.total) * 100
                    }
                except Exception as e:
                    disk_info[path] = {"error": str(e)}
            
            return {
                "success": True,
                "disk_info": disk_info,
                "paths_checked": len(paths)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"检查磁盘空间失败: {str(e)}"
            }
    
    def get_python_info(self) -> Dict[str, Any]:
        """获取Python环境信息"""
        try:
            return {
                "success": True,
                "python_info": {
                    "version": sys.version,
                    "version_info": sys.version_info._asdict(),
                    "executable": sys.executable,
                    "platform": sys.platform,
                    "path": sys.path,
                    "modules": list(sys.modules.keys()),
                    "builtin_module_names": sys.builtin_module_names,
                    "max_size": sys.maxsize,
                    "recursion_limit": sys.getrecursionlimit()
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"获取Python信息失败: {str(e)}"
            }
