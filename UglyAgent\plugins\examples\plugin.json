{"info": {"name": "hello_plugin", "version": "1.0.0", "description": "一个简单的Hello示例插件，演示UglyAgent插件系统的基本功能", "author": "UglyAgent Team", "license": "MIT", "homepage": "https://github.com/uglyagent/plugins", "dependencies": [], "python_requires": ">=3.7", "tags": ["example", "demo", "hello", "greeting"]}, "entry_point": "hello_plugin.HelloPlugin", "config_schema": {"type": "object", "properties": {"greeting": {"type": "string", "description": "默认问候语", "default": "Hello"}, "language": {"type": "string", "description": "默认语言", "enum": ["en", "zh", "es", "fr"], "default": "en"}, "max_greetings": {"type": "integer", "description": "最大问候次数", "minimum": 1, "maximum": 10000, "default": 1000}, "auto_respond": {"type": "boolean", "description": "是否自动回应问候", "default": true}}}, "permissions": ["read_config", "write_logs", "register_tools", "register_commands", "register_hooks"], "tools": [{"name": "say_hello", "description": "向指定的人打招呼", "parameters": {"name": {"type": "string", "description": "要问候的人的名字", "default": "World"}, "language": {"type": "string", "description": "问候语言", "enum": ["en", "zh", "es", "fr"]}}}, {"name": "get_greeting_stats", "description": "获取问候统计信息", "parameters": {}}, {"name": "set_greeting", "description": "设置自定义问候语", "parameters": {"greeting": {"type": "string", "description": "自定义问候语", "required": true}, "language": {"type": "string", "description": "问候语言", "default": "custom"}}}], "commands": [{"name": "hello", "description": "Hello命令，可以指定名字和语言", "usage": "hello [name] [language]", "examples": ["hello", "hello Alice", "hello Bob zh"]}, {"name": "stats", "description": "显示问候统计信息", "usage": "stats", "examples": ["stats"]}], "hooks": [{"event": "user_message", "description": "监听用户消息，自动回应问候"}, {"event": "session_start", "description": "会话开始时自动问候"}], "resources": [{"name": "greeting_templates", "description": "多语言问候语模板", "type": "dict"}]}