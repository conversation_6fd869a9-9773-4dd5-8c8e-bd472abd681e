#!/usr/bin/env python3
"""
UglyAgent LLM Manager - 大模型管理器
支持多种大模型API，负载均衡，成本优化等功能
"""

import asyncio
import json
import time
import random
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import yaml
import aiohttp
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class ModelProvider(Enum):
    """模型提供商枚举"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    CUSTOM = "custom"


@dataclass
class ModelConfig:
    """模型配置类"""
    name: str
    provider: ModelProvider
    model_name: str
    api_key: Optional[str] = None
    api_base: str = ""
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout: int = 60
    retry_attempts: int = 3
    headers: Dict[str, str] = None
    
    def __post_init__(self):
        if self.headers is None:
            self.headers = {}


@dataclass
class ModelResponse:
    """模型响应类"""
    content: str
    model: str
    tokens_used: int
    cost: float
    response_time: float
    success: bool
    error: Optional[str] = None


class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, strategy: str = "round_robin"):
        self.strategy = strategy
        self.current_index = 0
        self.usage_count = {}
    
    def select_model(self, available_models: List[str]) -> str:
        """选择模型"""
        if not available_models:
            raise ValueError("没有可用的模型")
        
        if self.strategy == "round_robin":
            model = available_models[self.current_index % len(available_models)]
            self.current_index += 1
            return model
        elif self.strategy == "random":
            return random.choice(available_models)
        elif self.strategy == "least_used":
            # 选择使用次数最少的模型
            least_used = min(available_models, 
                           key=lambda m: self.usage_count.get(m, 0))
            self.usage_count[least_used] = self.usage_count.get(least_used, 0) + 1
            return least_used
        else:
            return available_models[0]


class LLMManager:
    """大模型管理器"""
    
    def __init__(self, config_path: str = "config/models.yaml"):
    # def __init__(self, config_path: str = "models.yaml"):
        self.config_path = config_path
        self.models: Dict[str, ModelConfig] = {}
        self.default_model: str = ""
        self.load_balancer = LoadBalancer()
        self.session: Optional[aiohttp.ClientSession] = None
        self.metrics = {
            "requests": 0,
            "errors": 0,
            "total_cost": 0.0,
            "total_tokens": 0
        }
        
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                logger.warning(f"配置文件不存在: {self.config_path}")
                return
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.default_model = config.get('default_model', '')
            
            # 加载模型配置
            models_config = config.get('models', {})
            for name, model_config in models_config.items():
                provider = ModelProvider(model_config['provider'])
                self.models[name] = ModelConfig(
                    name=name,
                    provider=provider,
                    model_name=model_config['model_name'],
                    api_key=model_config.get('api_key'),
                    api_base=model_config.get('api_base', ''),
                    max_tokens=model_config.get('max_tokens', 4096),
                    temperature=model_config.get('temperature', 0.7),
                    timeout=model_config.get('timeout', 60),
                    retry_attempts=model_config.get('retry_attempts', 3),
                    headers=model_config.get('headers', {})
                )
            
            # 加载负载均衡配置
            lb_config = config.get('load_balancing', {})
            if lb_config.get('enabled', False):
                strategy = lb_config.get('strategy', 'round_robin')
                self.load_balancer = LoadBalancer(strategy)
            
            logger.info(f"已加载 {len(self.models)} 个模型配置")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def chat(self, 
                   message: str, 
                   model: Optional[str] = None,
                   system_prompt: Optional[str] = None,
                   **kwargs) -> ModelResponse:
        """发送聊天请求"""
        if not model:
            model = self.default_model
        
        if model not in self.models:
            raise ValueError(f"未知模型: {model}")
        
        model_config = self.models[model]
        
        # 重试机制
        for attempt in range(model_config.retry_attempts):
            try:
                response = await self._make_request(model_config, message, system_prompt, **kwargs)
                self.metrics["requests"] += 1
                return response
            except Exception as e:
                logger.warning(f"模型 {model} 请求失败 (尝试 {attempt + 1}): {e}")
                if attempt == model_config.retry_attempts - 1:
                    self.metrics["errors"] += 1
                    return ModelResponse(
                        content="",
                        model=model,
                        tokens_used=0,
                        cost=0.0,
                        response_time=0.0,
                        success=False,
                        error=str(e)
                    )
                await asyncio.sleep(2 ** attempt)  # 指数退避
    
    async def _make_request(self, 
                           config: ModelConfig, 
                           message: str,
                           system_prompt: Optional[str] = None,
                           **kwargs) -> ModelResponse:
        """发送HTTP请求到模型API"""
        start_time = time.time()
        
        if config.provider == ModelProvider.OPENAI:
            return await self._openai_request(config, message, system_prompt, **kwargs)
        elif config.provider == ModelProvider.ANTHROPIC:
            return await self._anthropic_request(config, message, system_prompt, **kwargs)
        elif config.provider == ModelProvider.OLLAMA:
            return await self._ollama_request(config, message, system_prompt, **kwargs)
        elif config.provider == ModelProvider.CUSTOM:
            return await self._custom_request(config, message, system_prompt, **kwargs)
        else:
            raise ValueError(f"不支持的模型提供商: {config.provider}")
    
    async def _openai_request(self, 
                             config: ModelConfig, 
                             message: str,
                             system_prompt: Optional[str] = None,
                             **kwargs) -> ModelResponse:
        """OpenAI API请求"""
        start_time = time.time()
        
        headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json",
            **config.headers
        }
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": message})
        
        data = {
            "model": config.model_name,
            "messages": messages,
            "max_tokens": config.max_tokens,
            "temperature": config.temperature,
            **kwargs
        }
        
        url = f"{config.api_base}/chat/completions"
        
        async with self.session.post(url, headers=headers, json=data, timeout=config.timeout) as resp:
            if resp.status != 200:
                error_text = await resp.text()
                raise Exception(f"OpenAI API错误 {resp.status}: {error_text}")
            
            result = await resp.json()
            
            content = result["choices"][0]["message"]["content"]
            tokens_used = result["usage"]["total_tokens"]
            
            # 简单的成本计算 (需要根据实际定价调整)
            cost = tokens_used * 0.00002  # 假设每token 0.00002美元
            
            response_time = time.time() - start_time
            
            return ModelResponse(
                content=content,
                model=config.name,
                tokens_used=tokens_used,
                cost=cost,
                response_time=response_time,
                success=True
            )
    
    async def _anthropic_request(self, 
                                config: ModelConfig, 
                                message: str,
                                system_prompt: Optional[str] = None,
                                **kwargs) -> ModelResponse:
        """Anthropic Claude API请求"""
        start_time = time.time()
        
        headers = {
            "x-api-key": config.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01",
            **config.headers
        }
        
        data = {
            "model": config.model_name,
            "max_tokens": config.max_tokens,
            "temperature": config.temperature,
            "messages": [{"role": "user", "content": message}],
            **kwargs
        }
        
        if system_prompt:
            data["system"] = system_prompt
        
        url = f"{config.api_base}/v1/messages"
        
        async with self.session.post(url, headers=headers, json=data, timeout=config.timeout) as resp:
            if resp.status != 200:
                error_text = await resp.text()
                raise Exception(f"Anthropic API错误 {resp.status}: {error_text}")
            
            result = await resp.json()
            
            content = result["content"][0]["text"]
            tokens_used = result["usage"]["input_tokens"] + result["usage"]["output_tokens"]
            
            # 简单的成本计算
            cost = tokens_used * 0.00003  # 假设每token 0.00003美元
            
            response_time = time.time() - start_time
            
            return ModelResponse(
                content=content,
                model=config.name,
                tokens_used=tokens_used,
                cost=cost,
                response_time=response_time,
                success=True
            )
    
    async def _ollama_request(self, 
                             config: ModelConfig, 
                             message: str,
                             system_prompt: Optional[str] = None,
                             **kwargs) -> ModelResponse:
        """Ollama本地模型请求"""
        start_time = time.time()
        
        headers = {
            "Content-Type": "application/json",
            **config.headers
        }
        
        prompt = message
        if system_prompt:
            prompt = f"System: {system_prompt}\n\nUser: {message}"
        
        data = {
            "model": config.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": config.temperature,
                "num_predict": config.max_tokens
            },
            **kwargs
        }
        
        url = f"{config.api_base}/api/generate"
        
        async with self.session.post(url, headers=headers, json=data, timeout=config.timeout) as resp:
            if resp.status != 200:
                error_text = await resp.text()
                raise Exception(f"Ollama API错误 {resp.status}: {error_text}")
            
            result = await resp.json()
            
            content = result["response"]
            tokens_used = result.get("eval_count", 0) + result.get("prompt_eval_count", 0)
            
            # 本地模型无成本
            cost = 0.0
            
            response_time = time.time() - start_time
            
            return ModelResponse(
                content=content,
                model=config.name,
                tokens_used=tokens_used,
                cost=cost,
                response_time=response_time,
                success=True
            )
    
    async def _custom_request(self, 
                             config: ModelConfig, 
                             message: str,
                             system_prompt: Optional[str] = None,
                             **kwargs) -> ModelResponse:
        """自定义API请求"""
        # 这里可以根据具体的自定义API实现
        # 暂时返回一个示例响应
        start_time = time.time()
        
        return ModelResponse(
            content="自定义模型响应示例",
            model=config.name,
            tokens_used=100,
            cost=0.001,
            response_time=time.time() - start_time,
            success=True
        )
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return list(self.models.keys())
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取使用指标"""
        return self.metrics.copy()
    
    def reset_metrics(self):
        """重置指标"""
        self.metrics = {
            "requests": 0,
            "errors": 0,
            "total_cost": 0.0,
            "total_tokens": 0
        }
