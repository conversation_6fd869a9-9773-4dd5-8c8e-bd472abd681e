#!/usr/bin/env python3
"""
UglyAgent 示例插件 - Hello Plugin
演示如何创建一个简单的插件
"""

import asyncio
import time
from typing import Dict, Any
import logging

from ..base import BasePlugin

logger = logging.getLogger(__name__)


class HelloPlugin(BasePlugin):
    """Hello 示例插件"""
    
    def __init__(self, name: str, version: str, description: str):
        super().__init__(name, version, description)
        
        # 设置插件信息
        self.info.author = "UglyAgent Team"
        self.info.license = "MIT"
        self.info.homepage = "https://github.com/uglyagent/plugins"
        self.info.tags = ["example", "demo", "hello"]
        
        # 插件状态
        self.greeting_count = 0
        self.start_time = 0
    
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """初始化插件"""
        try:
            self.logger.info("Hello Plugin 正在初始化...")
            
            # 处理配置
            if config:
                self.configure(config)
            
            # 设置默认配置
            default_config = {
                "greeting": "Hello",
                "language": "en",
                "max_greetings": 1000
            }
            
            for key, value in default_config.items():
                if key not in self.config:
                    self.config[key] = value
            
            self.logger.info("Hello Plugin 初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Hello Plugin 初始化失败: {e}")
            return False
    
    async def activate(self) -> bool:
        """激活插件"""
        try:
            self.logger.info("Hello Plugin 正在激活...")
            
            # 注册工具
            self.register_tool("say_hello", self.say_hello, "打招呼工具")
            self.register_tool("get_greeting_stats", self.get_greeting_stats, "获取打招呼统计")
            self.register_tool("set_greeting", self.set_greeting, "设置问候语")
            
            # 注册命令
            self.register_command("hello", self.hello_command, "Hello命令")
            self.register_command("stats", self.stats_command, "统计命令")
            
            # 注册钩子
            self.register_hook("user_message", self.on_user_message)
            self.register_hook("session_start", self.on_session_start)
            
            # 注册资源
            self.register_resource("greeting_templates", {
                "en": ["Hello", "Hi", "Hey", "Greetings"],
                "zh": ["你好", "您好", "嗨", "问候"],
                "es": ["Hola", "Buenos días", "Saludos"],
                "fr": ["Bonjour", "Salut", "Bonsoir"]
            })
            
            self.start_time = time.time()
            self.greeting_count = 0
            
            self.logger.info("Hello Plugin 激活完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Hello Plugin 激活失败: {e}")
            return False
    
    async def deactivate(self) -> bool:
        """停用插件"""
        try:
            self.logger.info("Hello Plugin 正在停用...")
            
            # 清理资源
            self.tools.clear()
            self.commands.clear()
            self.hooks.clear()
            self.resources.clear()
            
            self.logger.info(f"Hello Plugin 停用完成，共打招呼 {self.greeting_count} 次")
            return True
            
        except Exception as e:
            self.logger.error(f"Hello Plugin 停用失败: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """清理插件资源"""
        try:
            self.logger.info("Hello Plugin 正在清理...")
            
            # 重置状态
            self.greeting_count = 0
            self.start_time = 0
            self.config.clear()
            
            self.logger.info("Hello Plugin 清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Hello Plugin 清理失败: {e}")
            return False
    
    # 工具方法
    async def say_hello(self, name: str = "World", language: str = None) -> Dict[str, Any]:
        """打招呼工具"""
        try:
            if not language:
                language = self.get_config("language", "en")
            
            # 检查限制
            max_greetings = self.get_config("max_greetings", 1000)
            if self.greeting_count >= max_greetings:
                return {
                    "success": False,
                    "error": f"已达到最大问候次数限制: {max_greetings}"
                }
            
            # 获取问候语模板
            templates = self.resources.get("greeting_templates", {})
            greetings = templates.get(language, templates.get("en", ["Hello"]))
            
            # 选择问候语
            greeting = greetings[self.greeting_count % len(greetings)]
            message = f"{greeting}, {name}!"
            
            self.greeting_count += 1
            
            return {
                "success": True,
                "message": message,
                "language": language,
                "greeting_count": self.greeting_count
            }
            
        except Exception as e:
            self.logger.error(f"打招呼失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_greeting_stats(self) -> Dict[str, Any]:
        """获取打招呼统计"""
        uptime = time.time() - self.start_time if self.start_time > 0 else 0
        
        return {
            "success": True,
            "stats": {
                "total_greetings": self.greeting_count,
                "uptime_seconds": uptime,
                "average_greetings_per_minute": (self.greeting_count / (uptime / 60)) if uptime > 0 else 0,
                "current_language": self.get_config("language", "en"),
                "max_greetings": self.get_config("max_greetings", 1000)
            }
        }
    
    async def set_greeting(self, greeting: str, language: str = "custom") -> Dict[str, Any]:
        """设置自定义问候语"""
        try:
            if "greeting_templates" not in self.resources:
                self.resources["greeting_templates"] = {}
            
            if language not in self.resources["greeting_templates"]:
                self.resources["greeting_templates"][language] = []
            
            self.resources["greeting_templates"][language].append(greeting)
            
            return {
                "success": True,
                "message": f"已添加问候语: {greeting} ({language})"
            }
            
        except Exception as e:
            self.logger.error(f"设置问候语失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    # 命令方法
    async def hello_command(self, args: str = "") -> Dict[str, Any]:
        """Hello命令"""
        parts = args.strip().split() if args.strip() else []
        name = parts[0] if parts else "World"
        language = parts[1] if len(parts) > 1 else None
        
        return await self.say_hello(name, language)
    
    async def stats_command(self, args: str = "") -> Dict[str, Any]:
        """统计命令"""
        return await self.get_greeting_stats()
    
    # 钩子方法
    async def on_user_message(self, message: str, **kwargs) -> Dict[str, Any]:
        """用户消息钩子"""
        # 如果用户说了hello相关的词，自动回应
        hello_words = ["hello", "hi", "hey", "你好", "您好", "嗨"]
        
        if any(word in message.lower() for word in hello_words):
            self.logger.info("检测到问候语，自动回应")
            return await self.say_hello("User")
        
        return {"handled": False}
    
    async def on_session_start(self, session_id: str, **kwargs) -> Dict[str, Any]:
        """会话开始钩子"""
        self.logger.info(f"新会话开始: {session_id}")
        return await self.say_hello("New User")


# 插件入口点
def create_plugin():
    """创建插件实例"""
    return HelloPlugin(
        name="hello_plugin",
        version="1.0.0",
        description="一个简单的Hello示例插件"
    )
