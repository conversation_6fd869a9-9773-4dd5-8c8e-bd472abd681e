#!/usr/bin/env python3
"""
交互式MCP客户端 - 用于与MCP文件服务器交互
"""

import json
import subprocess
import sys
from typing import Dict, Any, Optional


class InteractiveMCPClient:
    """交互式MCP客户端"""
    
    def __init__(self):
        self.request_id = 0
        self.server_process = None
        self.initialized = False
    
    def get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def create_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建MCP请求"""
        request = {
            "jsonrpc": "2.0",
            "id": self.get_next_id(),
            "method": method
        }
        
        if params:
            request["params"] = params
        
        return request
    
    def send_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送请求到MCP服务器"""
        if not self.server_process:
            print("错误: 服务器未启动")
            return None

        try:
            # 发送请求
            request_json = json.dumps(request, ensure_ascii=False) + '\n'
            self.server_process.stdin.write(request_json)
            self.server_process.stdin.flush()

            # 读取响应
            response_line = self.server_process.stdout.readline().strip()
            if response_line:
                return json.loads(response_line)
            else:
                print("错误: 服务器无响应")
                return None

        except Exception as e:
            print(f"通信错误: {str(e)}")
            return None
    
    def start_server(self):
        """启动MCP服务器"""
        try:
            self.server_process = subprocess.Popen(
                [sys.executable, "mcp_server.py"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                bufsize=1
            )
            print("MCP服务器已启动")
            return True

        except Exception as e:
            print(f"启动服务器失败: {str(e)}")
            return False
    
    def stop_server(self):
        """停止MCP服务器"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            self.server_process = None
            print("MCP服务器已停止")
    
    def initialize(self):
        """初始化MCP连接"""
        init_request = self.create_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "interactive-client",
                "version": "1.0.0"
            }
        })
        
        response = self.send_request(init_request)
        if response and "result" in response:
            self.initialized = True
            print("MCP连接初始化成功")
            print(f"服务器: {response['result']['serverInfo']['name']}")
            print(f"版本: {response['result']['serverInfo']['version']}")
            return True
        else:
            print("初始化失败")
            return False
    
    def list_tools(self):
        """列出可用工具"""
        if not self.initialized:
            print("错误: 未初始化")
            return
        
        request = self.create_request("tools/list")
        response = self.send_request(request)
        
        if response and "result" in response:
            tools = response["result"]["tools"]
            print(f"\n可用工具 ({len(tools)} 个):")
            for i, tool in enumerate(tools, 1):
                print(f"{i}. {tool['name']} - {tool['description']}")
        else:
            print("获取工具列表失败")
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """调用工具"""
        if not self.initialized:
            print("错误: 未初始化")
            return
        
        request = self.create_request("tools/call", {
            "name": tool_name,
            "arguments": arguments
        })
        
        response = self.send_request(request)
        
        if response and "result" in response:
            content = response["result"]["content"][0]["text"]
            result = json.loads(content)
            
            if result["success"]:
                print("✅ 操作成功:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print("❌ 操作失败:")
                print(f"错误: {result['error']}")
        else:
            print("工具调用失败")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== MCP文件代理交互模式 ===")
        print("输入 'help' 查看帮助，输入 'quit' 退出")
        
        while True:
            try:
                command = input("\n> ").strip()
                
                if command == "quit":
                    break
                elif command == "help":
                    self.show_help()
                elif command == "tools":
                    self.list_tools()
                elif command.startswith("read "):
                    file_path = command[5:].strip()
                    self.call_tool("read_file", {"file_path": file_path})
                elif command.startswith("list "):
                    file_path = command[5:].strip()
                    self.call_tool("list_lines", {"file_path": file_path})
                elif command.startswith("modify "):
                    parts = command[7:].split(" ", 2)
                    if len(parts) >= 3:
                        file_path, line_num, new_content = parts
                        self.call_tool("modify_line", {
                            "file_path": file_path,
                            "line_number": int(line_num),
                            "new_content": new_content
                        })
                    else:
                        print("用法: modify <文件路径> <行号> <新内容>")
                elif command.startswith("save "):
                    parts = command[5:].split(" ", 1)
                    if len(parts) >= 2:
                        file_path, content = parts
                        self.call_tool("save_file", {
                            "file_path": file_path,
                            "content": content
                        })
                    else:
                        print("用法: save <文件路径> <内容>")
                else:
                    print("未知命令，输入 'help' 查看帮助")
            
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"错误: {str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        print("""
可用命令:
  help                           - 显示此帮助信息
  tools                          - 列出可用工具
  read <文件路径>                 - 读取文件内容
  list <文件路径>                 - 列出文件行
  modify <文件路径> <行号> <新内容> - 修改指定行
  save <文件路径> <内容>          - 保存文件
  quit                           - 退出程序

示例:
  read test.py
  list test.py
  modify test.py 1 "# 新的注释"
  save hello.py "print('Hello, World!')"
        """)
    
    def run(self):
        """运行客户端"""
        print("启动交互式MCP客户端...")
        
        if not self.start_server():
            return
        
        try:
            if self.initialize():
                self.interactive_mode()
        finally:
            self.stop_server()


def main():
    """主函数"""
    client = InteractiveMCPClient()
    client.run()


if __name__ == "__main__":
    main()
