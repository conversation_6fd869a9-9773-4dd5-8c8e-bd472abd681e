#!/usr/bin/env python3
"""
UglyAgent 任务规划器
提供智能任务分解、规划和执行管理功能
"""

import json
import time
import uuid
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import asyncio

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"      # 待执行
    RUNNING = "running"      # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    BLOCKED = "blocked"      # 被阻塞


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Task:
    """任务类"""
    id: str
    name: str
    description: str
    tool_name: str
    parameters: Dict[str, Any]
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    dependencies: List[str] = None  # 依赖的任务ID
    created_time: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0
    result: Any = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 超时时间(秒)
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.metadata is None:
            self.metadata = {}
        if self.created_time == 0.0:
            self.created_time = time.time()


@dataclass
class Plan:
    """执行计划"""
    id: str
    name: str
    description: str
    tasks: List[Task]
    created_time: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0
    status: TaskStatus = TaskStatus.PENDING
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.created_time == 0.0:
            self.created_time = time.time()


class TaskPlanner:
    """任务规划器"""
    
    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.plans: Dict[str, Plan] = {}
        self.running_tasks: Dict[str, Task] = {}
        self.task_queue: List[Task] = []
        self.completed_tasks: Dict[str, Task] = {}
        self.failed_tasks: Dict[str, Task] = {}
        
        # 任务执行器映射
        self.task_executors: Dict[str, Callable] = {}
        
        # 规划模板
        self.planning_templates = {
            "file_creation": self._create_file_creation_plan,
            "file_analysis": self._create_file_analysis_plan,
            "web_scraping": self._create_web_scraping_plan,
            "code_review": self._create_code_review_plan,
            "system_monitoring": self._create_system_monitoring_plan
        }
    
    def register_executor(self, tool_name: str, executor: Callable):
        """注册任务执行器"""
        self.task_executors[tool_name] = executor
        logger.info(f"已注册任务执行器: {tool_name}")
    
    def create_plan(self, goal: str, context: Dict[str, Any] = None) -> str:
        """创建执行计划"""
        try:
            plan_id = str(uuid.uuid4())

            print(f"🧠 UglyAgent 正在分析您的请求...")
            print(f"📝 目标: {goal}")

            # 分析目标，选择合适的规划模板
            plan_type = self._analyze_goal(goal, context or {})
            print(f"🎯 识别任务类型: {plan_type}")

            if plan_type in self.planning_templates:
                print(f"📋 使用专用规划模板: {plan_type}")
                tasks = self.planning_templates[plan_type](goal, context or {})
            else:
                print(f"📋 使用通用规划模板")
                tasks = self._create_generic_plan(goal, context or {})

            plan = Plan(
                id=plan_id,
                name=f"Plan for: {goal}",
                description=goal,
                tasks=tasks
            )

            self.plans[plan_id] = plan

            print(f"✅ 执行计划已创建 (ID: {plan_id[:8]}...)")
            print(f"📊 计划包含 {len(tasks)} 个任务:")

            for i, task in enumerate(tasks, 1):
                print(f"   {i}. {task.name}")
                print(f"      工具: {task.tool_name}")
                if task.dependencies:
                    dep_names = [self._get_task_name_by_id(dep_id, tasks) for dep_id in task.dependencies]
                    print(f"      依赖: {', '.join(dep_names)}")
                print(f"      优先级: {task.priority.name}")

            logger.info(f"创建执行计划: {plan_id} ({len(tasks)} 个任务)")
            return plan_id

        except Exception as e:
            logger.error(f"创建执行计划失败: {e}")
            return ""

    def _get_task_name_by_id(self, task_id: str, tasks: List[Task]) -> str:
        """根据任务ID获取任务名称"""
        for task in tasks:
            if task.id == task_id:
                return task.name
        return f"Task-{task_id[:8]}"
    
    def _analyze_goal(self, goal: str, context: Dict[str, Any]) -> str:
        """分析目标，确定计划类型"""
        goal_lower = goal.lower()

        if any(keyword in goal_lower for keyword in ["写", "创建", "生成", "保存", "write", "create", "generate", "save"]):
            return "file_creation"
        elif any(keyword in goal_lower for keyword in ["分析文件", "analyze file", "文件分析"]):
            return "file_analysis"
        elif any(keyword in goal_lower for keyword in ["爬取", "scrape", "抓取网页"]):
            return "web_scraping"
        elif any(keyword in goal_lower for keyword in ["代码审查", "code review", "检查代码"]):
            return "code_review"
        elif any(keyword in goal_lower for keyword in ["监控系统", "system monitor", "系统状态"]):
            return "system_monitoring"
        else:
            return "generic"

    def _create_file_creation_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建文件创建计划"""
        tasks = []

        # 从目标中提取文件路径和内容信息
        import re

        # 尝试提取文件路径
        file_path = None
        path_patterns = [
            r'保存为\s*([^\s,，]+)',
            r'保存到\s*([^\s,，]+)',
            r'写入\s*([^\s,，]+)',
            r'创建\s*([^\s,，]+)',
            r'save\s+(?:as|to)\s+([^\s,，]+)',
            r'write\s+(?:to|into)\s+([^\s,，]+)',
            r'create\s+([^\s,，]+)'
        ]

        for pattern in path_patterns:
            match = re.search(pattern, goal, re.IGNORECASE)
            if match:
                file_path = match.group(1)
                break

        if not file_path:
            file_path = context.get("file_path", "output.py")

        # 任务1: 生成代码内容
        task1 = Task(
            id=str(uuid.uuid4()),
            name="生成代码内容",
            description=f"根据需求生成代码: {goal}",
            tool_name="ai_chat",
            parameters={
                "message": goal,
                "system_prompt": "你是一个专业的程序员。请根据用户需求生成完整的、可运行的代码。只返回代码内容，不要包含解释或其他文字。"
            },
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)

        # 任务2: 保存文件
        task2 = Task(
            id=str(uuid.uuid4()),
            name="保存文件",
            description=f"将生成的代码保存到文件: {file_path}",
            tool_name="write_file",
            parameters={
                "file_path": file_path,
                "content": "{{task1_result}}"  # 占位符，将在执行时替换
            },
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)

        return tasks

    def _create_file_analysis_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建文件分析计划"""
        tasks = []
        file_path = context.get("file_path", "")
        
        # 任务1: 读取文件
        task1 = Task(
            id=str(uuid.uuid4()),
            name="读取文件",
            description=f"读取文件内容: {file_path}",
            tool_name="read_file",
            parameters={"file_path": file_path},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 分析代码结构
        task2 = Task(
            id=str(uuid.uuid4()),
            name="分析代码结构",
            description=f"分析代码文件结构: {file_path}",
            tool_name="analyze_code",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)
        
        # 任务3: 检查语法
        task3 = Task(
            id=str(uuid.uuid4()),
            name="检查语法",
            description=f"检查代码语法: {file_path}",
            tool_name="check_syntax",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task3)
        
        return tasks
    
    def _create_web_scraping_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建网页抓取计划"""
        tasks = []
        url = context.get("url", "")
        
        # 任务1: 检查URL状态
        task1 = Task(
            id=str(uuid.uuid4()),
            name="检查URL状态",
            description=f"检查URL可访问性: {url}",
            tool_name="check_url_status",
            parameters={"urls": [url]},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 抓取网页内容
        task2 = Task(
            id=str(uuid.uuid4()),
            name="抓取网页内容",
            description=f"抓取网页内容: {url}",
            tool_name="scrape_webpage",
            parameters={
                "url": url,
                "extract_links": True,
                "extract_images": True,
                "extract_text": True
            },
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)
        
        return tasks
    
    def _create_code_review_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建代码审查计划"""
        tasks = []
        file_path = context.get("file_path", "")
        
        # 任务1: 读取文件
        task1 = Task(
            id=str(uuid.uuid4()),
            name="读取代码文件",
            description=f"读取代码文件: {file_path}",
            tool_name="read_file",
            parameters={"file_path": file_path},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 代码分析
        task2 = Task(
            id=str(uuid.uuid4()),
            name="代码分析",
            description=f"分析代码质量: {file_path}",
            tool_name="analyze_code",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)
        
        # 任务3: 语法检查
        task3 = Task(
            id=str(uuid.uuid4()),
            name="语法检查",
            description=f"检查代码语法: {file_path}",
            tool_name="check_syntax",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task3)
        
        # 任务4: 提取函数
        task4 = Task(
            id=str(uuid.uuid4()),
            name="提取函数",
            description=f"提取函数定义: {file_path}",
            tool_name="extract_functions",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.LOW
        )
        tasks.append(task4)
        
        return tasks
    
    def _create_system_monitoring_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建系统监控计划"""
        tasks = []
        duration = context.get("duration", 60)
        
        # 任务1: 获取系统信息
        task1 = Task(
            id=str(uuid.uuid4()),
            name="获取系统信息",
            description="获取基本系统信息",
            tool_name="get_system_info",
            parameters={"info_type": "all"},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 监控系统资源
        task2 = Task(
            id=str(uuid.uuid4()),
            name="监控系统资源",
            description=f"监控系统资源使用情况 ({duration}秒)",
            tool_name="monitor_system",
            parameters={"duration": duration, "interval": 5},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL,
            timeout=duration + 30
        )
        tasks.append(task2)
        
        # 任务3: 检查磁盘空间
        task3 = Task(
            id=str(uuid.uuid4()),
            name="检查磁盘空间",
            description="检查磁盘空间使用情况",
            tool_name="check_disk_space",
            parameters={},
            priority=TaskPriority.LOW
        )
        tasks.append(task3)
        
        return tasks
    
    def _create_generic_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建通用计划"""
        # 简单的通用计划，可以根据需要扩展
        task = Task(
            id=str(uuid.uuid4()),
            name="执行目标",
            description=goal,
            tool_name="ai_chat",  # 使用AI对话来处理通用目标
            parameters={"message": goal},
            priority=TaskPriority.NORMAL
        )
        
        return [task]
    
    async def execute_plan(self, plan_id: str) -> Dict[str, Any]:
        """执行计划"""
        try:
            if plan_id not in self.plans:
                return {
                    "success": False,
                    "error": f"计划不存在: {plan_id}"
                }
            
            plan = self.plans[plan_id]
            plan.status = TaskStatus.RUNNING
            plan.start_time = time.time()
            
            logger.info(f"开始执行计划: {plan_id}")
            
            # 将任务添加到队列
            for task in plan.tasks:
                self.task_queue.append(task)
            
            # 执行任务
            await self._execute_tasks()
            
            # 检查计划完成状态
            completed_tasks = [t for t in plan.tasks if t.status == TaskStatus.COMPLETED]
            failed_tasks = [t for t in plan.tasks if t.status == TaskStatus.FAILED]
            
            if len(completed_tasks) == len(plan.tasks):
                plan.status = TaskStatus.COMPLETED
            elif failed_tasks:
                plan.status = TaskStatus.FAILED
            
            plan.end_time = time.time()
            
            return {
                "success": True,
                "plan_id": plan_id,
                "status": plan.status.value,
                "completed_tasks": len(completed_tasks),
                "failed_tasks": len(failed_tasks),
                "total_tasks": len(plan.tasks),
                "execution_time": plan.end_time - plan.start_time
            }
            
        except Exception as e:
            logger.error(f"执行计划失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_tasks(self):
        """执行任务队列"""
        while self.task_queue or self.running_tasks:
            # 启动可执行的任务
            await self._start_ready_tasks()
            
            # 等待运行中的任务
            if self.running_tasks:
                await asyncio.sleep(0.1)
            
            # 检查超时任务
            await self._check_timeout_tasks()
    
    async def _start_ready_tasks(self):
        """启动准备就绪的任务"""
        if len(self.running_tasks) >= self.max_concurrent_tasks:
            return
        
        ready_tasks = []
        
        for task in self.task_queue[:]:
            if self._is_task_ready(task):
                ready_tasks.append(task)
                self.task_queue.remove(task)
                
                if len(ready_tasks) + len(self.running_tasks) >= self.max_concurrent_tasks:
                    break
        
        # 按优先级排序
        ready_tasks.sort(key=lambda t: t.priority.value, reverse=True)
        
        for task in ready_tasks:
            await self._start_task(task)
    
    def _is_task_ready(self, task: Task) -> bool:
        """检查任务是否准备就绪"""
        if task.status != TaskStatus.PENDING:
            return False
        
        # 检查依赖任务是否完成
        for dep_id in task.dependencies:
            if dep_id not in self.completed_tasks:
                return False
        
        return True
    
    async def _start_task(self, task: Task):
        """启动任务"""
        try:
            task.status = TaskStatus.RUNNING
            task.start_time = time.time()
            self.running_tasks[task.id] = task

            print(f"\n🚀 开始执行任务: {task.name}")
            print(f"🔧 使用工具: {task.tool_name}")

            # 显示任务参数（隐藏敏感信息）
            safe_params = self._sanitize_parameters(task.parameters)
            if safe_params:
                print(f"⚙️ 任务参数:")
                for key, value in safe_params.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"   {key}: {value[:100]}...")
                    else:
                        print(f"   {key}: {value}")

            logger.info(f"启动任务: {task.name} ({task.id})")

            # 异步执行任务
            asyncio.create_task(self._execute_task(task))

        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            task.status = TaskStatus.FAILED
            task.error = str(e)

    def _sanitize_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """清理参数，隐藏敏感信息"""
        safe_params = {}
        sensitive_keys = ['api_key', 'password', 'token', 'secret']

        for key, value in params.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                safe_params[key] = "***"
            else:
                safe_params[key] = value

        return safe_params
    
    async def _execute_task(self, task: Task):
        """执行单个任务"""
        try:
            executor = self.task_executors.get(task.tool_name)
            if not executor:
                raise ValueError(f"未找到任务执行器: {task.tool_name}")

            print(f"⚡ 正在执行: {task.name}")

            # 处理参数中的占位符
            processed_params = self._process_task_parameters(task)

            # 显示处理后的参数变化
            if processed_params != task.parameters:
                print(f"🔄 参数处理完成，已替换占位符")
                for key, value in processed_params.items():
                    if key in task.parameters and str(task.parameters[key]) != str(value):
                        if isinstance(value, str) and len(value) > 200:
                            print(f"   {key}: [内容已从依赖任务获取，长度: {len(value)} 字符]")
                        else:
                            print(f"   {key}: {value}")

            # 特殊处理AI对话任务，显示交互详情
            if task.tool_name == "ai_chat":
                await self._execute_ai_chat_with_details(task, processed_params)
            else:
                print(f"🔧 调用工具: {task.tool_name}")
                # 执行任务
                result = await executor(**processed_params)
                task.result = result

            task.status = TaskStatus.COMPLETED
            task.end_time = time.time()
            execution_time = task.end_time - task.start_time

            # 显示任务完成信息
            print(f"✅ 任务完成: {task.name} (耗时: {execution_time:.2f}秒)")

            # 显示结果摘要
            if task.result:
                self._display_task_result(task)

            # 移动到完成列表
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            self.completed_tasks[task.id] = task

            logger.info(f"任务完成: {task.name} ({task.id})")

        except Exception as e:
            print(f"❌ 任务执行失败: {task.name}")
            print(f"   错误: {str(e)}")

            logger.error(f"任务执行失败: {task.name} - {e}")

            task.error = str(e)
            task.retry_count += 1

            if task.retry_count < task.max_retries:
                # 重试任务
                print(f"🔄 准备重试任务 (第{task.retry_count}次)")
                task.status = TaskStatus.PENDING
                self.task_queue.append(task)
                logger.info(f"任务重试: {task.name} (第{task.retry_count}次)")
            else:
                # 任务失败
                print(f"💥 任务最终失败，已达到最大重试次数")
                task.status = TaskStatus.FAILED
                task.end_time = time.time()
                self.failed_tasks[task.id] = task

            # 从运行列表移除
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]

    def _process_task_parameters(self, task: Task) -> Dict[str, Any]:
        """处理任务参数中的占位符"""
        processed_params = {}

        for key, value in task.parameters.items():
            if isinstance(value, str) and value.startswith("{{") and value.endswith("}}"):
                # 处理占位符
                placeholder = value[2:-2]  # 移除 {{ 和 }}

                if placeholder == "task1_result":
                    # 查找第一个依赖任务的结果
                    if task.dependencies:
                        dep_task_id = task.dependencies[0]
                        if dep_task_id in self.completed_tasks:
                            dep_task = self.completed_tasks[dep_task_id]
                            if dep_task.result:
                                # 提取AI生成的内容
                                if isinstance(dep_task.result, dict):
                                    if "content" in dep_task.result:
                                        processed_params[key] = dep_task.result["content"]
                                    elif "response" in dep_task.result:
                                        processed_params[key] = dep_task.result["response"]
                                    else:
                                        processed_params[key] = str(dep_task.result)
                                else:
                                    processed_params[key] = str(dep_task.result)
                            else:
                                processed_params[key] = ""
                        else:
                            processed_params[key] = ""
                    else:
                        processed_params[key] = ""
                else:
                    processed_params[key] = value
            else:
                processed_params[key] = value

        return processed_params

    async def _execute_ai_chat_with_details(self, task: Task, params: Dict[str, Any]):
        """执行AI对话任务并显示详细过程"""
        print(f"🤖 准备与AI模型对话...")

        # 显示发送给AI的信息
        message = params.get("message", "")
        system_prompt = params.get("system_prompt", "")

        if system_prompt:
            print(f"📋 系统提示:")
            print(f"   {system_prompt}")

        print(f"💬 发送消息:")
        if len(message) > 300:
            print(f"   {message[:300]}...")
            print(f"   [消息总长度: {len(message)} 字符]")
        else:
            print(f"   {message}")

        print(f"⏳ 等待AI响应...")

        # 执行AI对话
        executor = self.task_executors.get("ai_chat")
        result = await executor(**params)

        # 显示AI响应
        if result and result.get("success"):
            response_content = result.get("content", "")
            print(f"🎯 AI响应:")

            if len(response_content) > 500:
                print(f"   {response_content[:500]}...")
                print(f"   [响应总长度: {len(response_content)} 字符]")
            else:
                print(f"   {response_content}")

            # 显示模型信息
            if "model" in result:
                print(f"🏷️ 使用模型: {result['model']}")
            if "tokens_used" in result:
                print(f"🔢 消耗Token: {result['tokens_used']}")
            if "response_time" in result:
                print(f"⏱️ 响应时间: {result['response_time']:.2f}秒")
        else:
            print(f"❌ AI响应失败: {result.get('error', '未知错误')}")

        task.result = result

    def _display_task_result(self, task: Task):
        """显示任务结果摘要"""
        result = task.result

        if not result:
            return

        if isinstance(result, dict):
            if result.get("success"):
                print(f"📊 任务结果:")

                # 文件操作结果
                if "file_path" in result:
                    print(f"   📁 文件路径: {result['file_path']}")
                if "size" in result:
                    print(f"   📏 文件大小: {result['size']} bytes")

                # AI对话结果
                if "content" in result:
                    content = result["content"]
                    if isinstance(content, str) and len(content) > 100:
                        print(f"   📝 生成内容: [长度: {len(content)} 字符]")
                    else:
                        print(f"   📝 内容: {content}")

                # HTTP请求结果
                if "status_code" in result:
                    print(f"   🌐 HTTP状态: {result['status_code']}")

                # 其他有用信息
                for key, value in result.items():
                    if key not in ["success", "content", "file_path", "size", "status_code", "error"]:
                        if isinstance(value, (str, int, float, bool)):
                            print(f"   {key}: {value}")
            else:
                print(f"❌ 任务执行失败: {result.get('error', '未知错误')}")
        else:
            print(f"📊 任务结果: {result}")
    
    async def _check_timeout_tasks(self):
        """检查超时任务"""
        current_time = time.time()
        
        for task_id, task in list(self.running_tasks.items()):
            if current_time - task.start_time > task.timeout:
                logger.warning(f"任务超时: {task.name} ({task.id})")
                
                task.status = TaskStatus.FAILED
                task.error = "任务执行超时"
                task.end_time = current_time
                
                del self.running_tasks[task_id]
                self.failed_tasks[task_id] = task
    
    def get_plan_status(self, plan_id: str) -> Dict[str, Any]:
        """获取计划状态"""
        if plan_id not in self.plans:
            return {
                "success": False,
                "error": f"计划不存在: {plan_id}"
            }
        
        plan = self.plans[plan_id]
        
        task_stats = {
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0,
            "blocked": 0
        }
        
        for task in plan.tasks:
            task_stats[task.status.value] += 1
        
        return {
            "success": True,
            "plan_id": plan_id,
            "plan_name": plan.name,
            "plan_status": plan.status.value,
            "task_stats": task_stats,
            "total_tasks": len(plan.tasks),
            "created_time": plan.created_time,
            "start_time": plan.start_time,
            "end_time": plan.end_time
        }
    
    def cancel_plan(self, plan_id: str) -> Dict[str, Any]:
        """取消计划"""
        try:
            if plan_id not in self.plans:
                return {
                    "success": False,
                    "error": f"计划不存在: {plan_id}"
                }
            
            plan = self.plans[plan_id]
            
            # 取消所有相关任务
            cancelled_count = 0
            for task in plan.tasks:
                if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                    task.status = TaskStatus.CANCELLED
                    task.end_time = time.time()
                    cancelled_count += 1
                    
                    # 从队列和运行列表中移除
                    if task in self.task_queue:
                        self.task_queue.remove(task)
                    if task.id in self.running_tasks:
                        del self.running_tasks[task.id]
            
            plan.status = TaskStatus.CANCELLED
            plan.end_time = time.time()
            
            logger.info(f"计划已取消: {plan_id} ({cancelled_count} 个任务)")
            
            return {
                "success": True,
                "plan_id": plan_id,
                "cancelled_tasks": cancelled_count
            }
            
        except Exception as e:
            logger.error(f"取消计划失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
