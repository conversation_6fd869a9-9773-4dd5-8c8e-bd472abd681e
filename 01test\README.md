# MCP File Agent

一个使用MCP（Model Context Protocol）接口的文件操作代理，支持读取、修改和保存txt和py文件。

## 功能特性

- ✅ 读取txt和py文件内容
- ✅ 列出文件的所有行及其行号
- ✅ 修改文件中指定行的内容
- ✅ 保存文件内容
- ✅ 错误处理和验证
- ✅ 支持UTF-8编码
- ✅ 命令行接口和编程接口

## 支持的文件类型

- `.txt` - 文本文件
- `.py` - Python源代码文件

## 安装和使用

### 1. 直接使用Python类

```python
from file_agent import MCPFileAgent

# 创建agent实例
agent = MCPFileAgent()

# 读取文件
result = agent.handle_request("read_file", {"file_path": "example.py"})
print(result)

# 修改文件中的某一行
result = agent.handle_request("modify_line", {
    "file_path": "example.py",
    "line_number": 5,
    "new_content": "print('Hello, Modified World!')"
})
print(result)

# 保存文件
result = agent.handle_request("save_file", {
    "file_path": "new_file.py",
    "content": "print('Hello, World!')\n"
})
print(result)
```

### 2. 命令行接口

```bash
# 显示可用工具
python file_agent.py tools

# 读取文件
python file_agent.py read example.py

# 列出文件行
python file_agent.py list example.py

# 修改指定行
python file_agent.py modify example.py 5 "print('新的内容')"

# 保存文件
python file_agent.py save new_file.txt "这是新文件的内容"
```

## 可用工具

### 1. read_file
读取txt或py文件的内容

**参数:**
- `file_path` (string): 要读取的文件路径

**返回:**
```json
{
  "success": true,
  "content": "文件内容",
  "file_path": "example.py",
  "line_count": 10
}
```

### 2. list_lines
列出文件的所有行及其行号

**参数:**
- `file_path` (string): 要列出行的文件路径

**返回:**
```json
{
  "success": true,
  "lines": [
    {"line_number": 1, "content": "第一行内容"},
    {"line_number": 2, "content": "第二行内容"}
  ],
  "total_lines": 2,
  "file_path": "example.txt"
}
```

### 3. modify_line
修改文件中指定行的内容

**参数:**
- `file_path` (string): 要修改的文件路径
- `line_number` (integer): 要修改的行号（从1开始）
- `new_content` (string): 新的行内容

**返回:**
```json
{
  "success": true,
  "file_path": "example.py",
  "line_number": 5,
  "original_content": "原始内容",
  "new_content": "新内容",
  "message": "成功修改第 5 行"
}
```

### 4. save_file
保存文件内容

**参数:**
- `file_path` (string): 要保存的文件路径
- `content` (string): 文件内容

**返回:**
```json
{
  "success": true,
  "file_path": "new_file.py",
  "message": "文件保存成功: new_file.py",
  "size": 1024
}
```

## 运行示例

### 完整功能演示

运行演示程序来查看所有功能的完整演示：

```bash
python demo.py
```

这将展示：
- 工具信息和支持的文件类型
- Python文件的创建、读取、修改操作
- 文本文件的操作
- 错误处理演示

### 基础示例

运行基础示例程序：

```bash
python example_usage.py
```

这将创建测试文件，演示读取、修改和保存操作，并显示错误处理。

### 完整测试

运行完整测试套件：

```bash
python test_all.py
```

这将测试所有功能模块，包括基本操作、命令行接口、MCP服务器和错误处理。

## 错误处理

Agent会处理以下错误情况：

- 文件不存在
- 不支持的文件类型
- 行号超出范围
- 文件权限问题
- 编码问题

所有错误都会返回包含错误信息的JSON响应：

```json
{
  "success": false,
  "error": "错误描述"
}
```

## MCP服务器模式

除了直接使用Python类，还提供了符合MCP协议标准的服务器实现：

### 启动MCP服务器

```bash
# 启动MCP服务器（通过stdin/stdout通信）
python mcp_server.py

# 测试MCP服务器
python mcp_server.py test
```

### 使用交互式客户端

```bash
# 启动交互式客户端
python interactive_client.py
```

交互式客户端支持以下命令：
- `tools` - 列出可用工具
- `read <文件路径>` - 读取文件
- `list <文件路径>` - 列出文件行
- `modify <文件路径> <行号> <新内容>` - 修改指定行
- `save <文件路径> <内容>` - 保存文件
- `help` - 显示帮助
- `quit` - 退出

### MCP配置

可以使用 `mcp_config.json` 配置文件将此服务器集成到支持MCP的应用中。

## 项目结构

```
.
├── file_agent.py         # 主要的MCP文件代理类
├── mcp_server.py         # MCP协议服务器实现
├── interactive_client.py # 交互式MCP客户端
├── example_usage.py      # 基础使用示例
├── demo.py              # 完整功能演示
├── test_all.py          # 完整测试套件
├── mcp_config.json       # MCP服务器配置文件
├── README.md            # 说明文档
├── test_file.py         # 示例运行后生成的测试文件
├── test_file.txt        # 示例运行后生成的测试文件
├── demo_example.py      # 演示生成的Python文件
└── demo_readme.txt      # 演示生成的文本文件
```

## 扩展

要添加对其他文件类型的支持，只需修改 `MCPFileAgent` 类中的 `supported_extensions` 集合：

```python
self.supported_extensions = {'.txt', '.py', '.js', '.html', '.css'}
```

## 许可证

MIT License
