# UglyAgent - 超越Augment的AI Agent

UglyAgent是一个强大的AI Agent系统，使用MCP协议，支持多种大模型API，具有丰富的工具集和可扩展的插件系统。

## 🚀 核心特性

### 比Augment更强大的功能
- **多模型支持**: 支持OpenAI、Claude、本地模型等多种大模型API
- **智能对话**: 上下文感知的对话管理和记忆系统
- **任务规划**: 自动分解复杂任务并执行
- **丰富工具集**: 文件操作、代码分析、网络请求、系统操作等
- **插件系统**: 可扩展的插件架构，支持自定义工具
- **MCP协议**: 完全兼容MCP协议标准
- **实时学习**: 从交互中学习和改进

### 架构设计

```
UglyAgent/
├── core/                   # 核心引擎
│   ├── agent.py           # 主Agent类
│   ├── llm_manager.py     # 大模型管理器
│   ├── memory.py          # 记忆系统
│   ├── planner.py         # 任务规划器
│   └── executor.py        # 执行引擎
├── mcp/                   # MCP协议实现
│   ├── server.py          # MCP服务器
│   ├── client.py          # MCP客户端
│   └── protocol.py        # 协议处理
├── tools/                 # 工具集
│   ├── base.py           # 工具基类
│   ├── file_ops.py       # 文件操作工具
│   ├── code_analysis.py  # 代码分析工具
│   ├── web_tools.py      # 网络工具
│   ├── system_tools.py   # 系统工具
│   └── custom/           # 自定义工具目录
├── plugins/              # 插件系统
│   ├── manager.py        # 插件管理器
│   ├── base.py          # 插件基类
│   └── examples/        # 示例插件
├── config/              # 配置系统
│   ├── settings.py      # 配置管理
│   ├── models.yaml      # 模型配置
│   └── tools.yaml       # 工具配置
├── utils/               # 工具函数
│   ├── logger.py        # 日志系统
│   ├── helpers.py       # 辅助函数
│   └── validators.py    # 验证器
├── examples/            # 示例和演示
├── tests/              # 测试用例
└── docs/               # 文档
```

## 🎯 设计目标

1. **超越Augment**: 提供更强大的功能和更好的用户体验
2. **易于使用**: 简单的配置和直观的API
3. **高度可扩展**: 插件系统支持无限扩展
4. **性能优异**: 高效的执行引擎和智能缓存
5. **稳定可靠**: 完善的错误处理和恢复机制

## 🔧 核心组件

### 1. AI Agent引擎
- 智能对话处理
- 上下文管理
- 任务分解和规划
- 工具选择和调用

### 2. 大模型管理器
- 多API支持 (OpenAI, Claude, 本地模型)
- 自动负载均衡
- 错误重试和降级
- 成本优化

### 3. 工具系统
- 标准化工具接口
- 动态工具加载
- 工具组合和链式调用
- 安全沙箱执行

### 4. 插件系统
- 热插拔插件加载
- 插件依赖管理
- 插件安全验证
- 插件市场支持

### 5. MCP协议
- 完整MCP协议实现
- 高性能通信
- 错误处理和恢复
- 协议扩展支持

## 🚀 快速开始

```bash
# 安装依赖
pip install -r requirements.txt

# 配置模型API
cp config/models.yaml.example config/models.yaml
# 编辑 config/models.yaml 添加你的API密钥

# 启动UglyAgent
python main.py

# 或者作为MCP服务器启动
python -m UglyAgent.mcp.server
```

## 📖 使用示例

```python
from UglyAgent import UglyAgent

# 创建Agent实例
agent = UglyAgent(config_path="config/models.yaml")

# 简单对话
response = agent.chat("帮我分析这个Python文件的复杂度")

# 执行复杂任务
result = agent.execute_task(
    "创建一个Web爬虫，爬取新闻网站并分析情感"
)
```

## 🔌 插件开发

```python
from UglyAgent.plugins.base import BasePlugin

class MyPlugin(BasePlugin):
    def __init__(self):
        super().__init__("my_plugin", "1.0.0")
    
    def get_tools(self):
        return {
            "my_tool": {
                "description": "我的自定义工具",
                "function": self.my_tool_function
            }
        }
    
    def my_tool_function(self, **kwargs):
        # 工具实现
        return {"result": "success"}
```

## 🎯 与Augment的对比

| 特性 | UglyAgent | Augment |
|------|-----------|---------|
| 多模型支持 | ✅ 支持所有主流模型 | ❌ 仅支持Claude |
| 插件系统 | ✅ 完整插件架构 | ❌ 有限扩展性 |
| 任务规划 | ✅ 智能任务分解 | ⚠️ 基础规划 |
| 记忆系统 | ✅ 长期记忆 | ⚠️ 会话记忆 |
| 工具丰富度 | ✅ 50+ 内置工具 | ⚠️ 基础工具集 |
| 性能优化 | ✅ 多重优化 | ⚠️ 标准性能 |
| 开源程度 | ✅ 完全开源 | ❌ 闭源 |

## 📝 许可证

MIT License - 完全开源，自由使用和修改。

---

**UglyAgent - 让AI Agent更强大，让开发更简单！**

## 🚀 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/uglyagent/uglyagent.git
cd uglyagent

# 运行安装脚本
chmod +x scripts/install.sh
./scripts/install.sh

# 或者手动安装
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 配置

1. 复制配置文件模板：
```bash
cp config/models.yaml.example config/models.yaml
cp config/config.yaml.example config/config.yaml
```

2. 编辑 `config/models.yaml` 添加你的API密钥：
```yaml
default_model: "openai_gpt4"

models:
  openai_gpt4:
    provider: "openai"
    model_name: "gpt-4"
    api_key: "your_openai_api_key_here"
    # ... 其他配置
```

### 启动

```bash
# 使用启动脚本（推荐）
chmod +x scripts/start.sh
./scripts/start.sh

# 或者直接启动
python main.py                    # 交互模式
python main.py mcp-stdio          # MCP服务器模式
python main.py api               # API服务器模式
```

## 📖 使用指南

### 交互模式

启动交互模式后，你可以直接与UglyAgent对话：

```
👤 您: 帮我分析这个Python文件: example.py
🤖 UglyAgent: 正在分析文件...
📋 使用了任务规划，执行了 3 个任务

👤 您: 爬取这个网页的内容: https://example.com
🤖 UglyAgent: 正在抓取网页内容...
```

### MCP协议模式

UglyAgent完全兼容MCP协议，可以作为MCP服务器使用：

```bash
# 启动MCP服务器
python main.py mcp-stdio

# 或者TCP模式
python main.py mcp-tcp --host localhost --port 8080
```

### API服务器模式

启动HTTP API服务器：

```bash
python main.py api --host 0.0.0.0 --port 8000
```

API端点：
- `POST /chat` - 对话接口
- `POST /execute_task` - 任务执行接口
- `GET /status` - 获取状态
- `GET /plugins` - 插件信息
- `GET /health` - 健康检查

### Python API

```python
import asyncio
from UglyAgent import UglyAgent

async def main():
    async with UglyAgent() as agent:
        # 简单对话
        response = await agent.chat("你好，请介绍一下自己")
        print(response["response"])

        # 执行复杂任务
        result = await agent.execute_task(
            "分析这个代码文件的复杂度",
            {"file_path": "example.py"}
        )
        print(result)

asyncio.run(main())
```

## 🔧 功能特性

### 文件操作

```python
# 读取文件
response = await agent.chat("读取文件 config.yaml 的内容")

# 搜索文件
response = await agent.chat("在当前目录搜索包含 'TODO' 的Python文件")

# 分析代码
response = await agent.chat("分析 main.py 的代码结构和复杂度")
```

### 网络工具

```python
# 网页抓取
response = await agent.chat("抓取 https://news.ycombinator.com 的标题")

# API调用
response = await agent.chat("调用 https://api.github.com/users/octocat")

# 批量URL检查
response = await agent.chat("检查这些URL的状态: url1, url2, url3")
```

### 系统操作

```python
# 系统信息
response = await agent.chat("获取系统CPU和内存使用情况")

# 执行命令
response = await agent.chat("列出当前目录的文件")

# 监控系统
response = await agent.chat("监控系统资源使用情况60秒")
```

### 任务规划

UglyAgent会自动识别复杂任务并创建执行计划：

```python
# 复杂任务会自动使用规划器
response = await agent.chat(
    "帮我做一个完整的代码审查：读取所有Python文件，"
    "分析代码质量，检查语法错误，生成报告"
)
```

## 🔌 插件系统

### 使用插件

```bash
# 列出插件
python main.py
> plugin list

# 激活插件
> plugin activate hello_plugin

# 查看插件信息
> plugin info hello_plugin
```

### 开发插件

创建插件目录结构：
```
plugins/my_plugin/
├── plugin.json          # 插件清单
├── my_plugin.py         # 插件代码
└── __init__.py
```

插件代码示例：
```python
from UglyAgent.plugins.base import BasePlugin

class MyPlugin(BasePlugin):
    def __init__(self, name, version, description):
        super().__init__(name, version, description)

    async def initialize(self, config=None):
        # 注册工具
        self.register_tool("my_tool", self.my_tool_function)
        return True

    async def activate(self):
        return True

    async def deactivate(self):
        return True

    async def cleanup(self):
        return True

    async def my_tool_function(self, param1, param2="default"):
        return {"result": f"处理了 {param1} 和 {param2}"}
```

插件清单 (`plugin.json`)：
```json
{
  "info": {
    "name": "my_plugin",
    "version": "1.0.0",
    "description": "我的自定义插件",
    "author": "Your Name"
  },
  "entry_point": "my_plugin.MyPlugin",
  "tools": [
    {
      "name": "my_tool",
      "description": "我的工具函数"
    }
  ]
}
```

## ⚙️ 配置

### 主配置文件 (config/config.yaml)

```yaml
# Agent基础配置
agent:
  name: "UglyAgent"
  debug: false
  log_level: "INFO"

# 性能配置
performance:
  max_concurrent_requests: 10
  cache_enabled: true
  cache_ttl: 3600

# 安全配置
security:
  encrypt_api_keys: true
  filter_sensitive_data: true
  allowed_file_extensions: [".txt", ".py", ".js"]
```

### 模型配置 (config/models.yaml)

```yaml
default_model: "openai_gpt4"

models:
  openai_gpt4:
    provider: "openai"
    model_name: "gpt-4"
    api_key: "your_api_key"
    max_tokens: 4096
    temperature: 0.7

  claude_sonnet:
    provider: "anthropic"
    model_name: "claude-3-sonnet-20240229"
    api_key: "your_api_key"
    max_tokens: 4096
    temperature: 0.7
```

## 🧪 测试

```bash
# 运行所有测试
pytest tests/ -v

# 运行特定测试
pytest tests/test_agent.py -v

# 运行覆盖率测试
pytest tests/ --cov=UglyAgent --cov-report=html
```

## 📊 监控和日志

### 日志配置

日志文件位置：`logs/uglyagent.log`

日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL

### 性能监控

```python
# 获取Agent状态
status = agent.get_agent_status()
print(status)

# 获取插件状态
plugin_status = plugin_manager.get_plugin_status()
print(plugin_status)
```

## 🔒 安全

### API密钥管理

- 使用环境变量存储敏感信息
- 支持API密钥加密
- 配置文件权限控制

### 文件操作安全

- 文件类型白名单
- 路径遍历保护
- 文件大小限制

### 命令执行安全

- 危险命令黑名单
- 安全模式执行
- 超时保护

## 🚀 部署

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt
EXPOSE 8000

CMD ["python", "main.py", "api", "--host", "0.0.0.0"]
```

### 系统服务

创建systemd服务文件：
```ini
[Unit]
Description=UglyAgent Service
After=network.target

[Service]
Type=simple
User=uglyagent
WorkingDirectory=/opt/uglyagent
ExecStart=/opt/uglyagent/venv/bin/python main.py api
Restart=always

[Install]
WantedBy=multi-user.target
```

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/uglyagent/uglyagent.git
cd uglyagent

# 安装开发依赖
pip install -e ".[dev]"

# 运行代码检查
flake8 UglyAgent/
mypy UglyAgent/
black UglyAgent/

# 运行测试
pytest tests/ -v
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢所有贡献者和开源社区的支持！

## 📞 联系我们

- GitHub: https://github.com/uglyagent/uglyagent
- 文档: https://uglyagent.readthedocs.io
- 问题反馈: https://github.com/uglyagent/uglyagent/issues

---
