#!/usr/bin/env python3
"""
UglyAgent 插件开发示例
演示如何开发自定义插件
"""

import asyncio
import json
import time
import random
from typing import Dict, Any, List
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from UglyAgent.plugins.base import BasePlugin
from UglyAgent.plugins.manager import PluginManager


class WeatherPlugin(BasePlugin):
    """天气插件示例"""
    
    def __init__(self, name: str, version: str, description: str):
        super().__init__(name, version, description)
        
        # 设置插件信息
        self.info.author = "UglyAgent Team"
        self.info.license = "MIT"
        self.info.tags = ["weather", "api", "utility"]
        
        # 模拟天气数据
        self.weather_data = {
            "北京": {"temperature": 15, "humidity": 60, "condition": "晴天"},
            "上海": {"temperature": 18, "humidity": 70, "condition": "多云"},
            "广州": {"temperature": 25, "humidity": 80, "condition": "小雨"},
            "深圳": {"temperature": 24, "humidity": 75, "condition": "阴天"},
            "杭州": {"temperature": 16, "humidity": 65, "condition": "晴天"}
        }
    
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """初始化插件"""
        try:
            self.logger.info("Weather Plugin 正在初始化...")
            
            # 处理配置
            if config:
                self.configure(config)
            
            # 设置默认配置
            default_config = {
                "api_key": "",
                "default_city": "北京",
                "units": "celsius",
                "cache_duration": 300  # 5分钟缓存
            }
            
            for key, value in default_config.items():
                if key not in self.config:
                    self.config[key] = value
            
            self.logger.info("Weather Plugin 初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Weather Plugin 初始化失败: {e}")
            return False
    
    async def activate(self) -> bool:
        """激活插件"""
        try:
            self.logger.info("Weather Plugin 正在激活...")
            
            # 注册工具
            self.register_tool("get_weather", self.get_weather, "获取天气信息")
            self.register_tool("get_forecast", self.get_forecast, "获取天气预报")
            self.register_tool("search_cities", self.search_cities, "搜索城市")
            
            # 注册命令
            self.register_command("weather", self.weather_command, "天气命令")
            self.register_command("forecast", self.forecast_command, "预报命令")
            
            # 注册钩子
            self.register_hook("user_message", self.on_user_message)
            
            # 注册资源
            self.register_resource("weather_data", self.weather_data)
            self.register_resource("supported_cities", list(self.weather_data.keys()))
            
            self.logger.info("Weather Plugin 激活完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Weather Plugin 激活失败: {e}")
            return False
    
    async def deactivate(self) -> bool:
        """停用插件"""
        try:
            self.logger.info("Weather Plugin 正在停用...")
            
            # 清理资源
            self.tools.clear()
            self.commands.clear()
            self.hooks.clear()
            self.resources.clear()
            
            self.logger.info("Weather Plugin 停用完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Weather Plugin 停用失败: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """清理插件资源"""
        try:
            self.logger.info("Weather Plugin 正在清理...")
            
            # 清理缓存等资源
            self.config.clear()
            
            self.logger.info("Weather Plugin 清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Weather Plugin 清理失败: {e}")
            return False
    
    # 工具方法
    async def get_weather(self, city: str = None, units: str = None) -> Dict[str, Any]:
        """获取天气信息"""
        try:
            if not city:
                city = self.get_config("default_city", "北京")
            
            if not units:
                units = self.get_config("units", "celsius")
            
            # 模拟API调用延迟
            await asyncio.sleep(0.1)
            
            if city in self.weather_data:
                weather = self.weather_data[city].copy()
                
                # 添加一些随机变化
                weather["temperature"] += random.randint(-3, 3)
                weather["humidity"] += random.randint(-10, 10)
                weather["timestamp"] = time.time()
                
                return {
                    "success": True,
                    "city": city,
                    "weather": weather,
                    "units": units
                }
            else:
                return {
                    "success": False,
                    "error": f"不支持的城市: {city}",
                    "supported_cities": list(self.weather_data.keys())
                }
                
        except Exception as e:
            self.logger.error(f"获取天气失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_forecast(self, city: str = None, days: int = 3) -> Dict[str, Any]:
        """获取天气预报"""
        try:
            if not city:
                city = self.get_config("default_city", "北京")
            
            if city not in self.weather_data:
                return {
                    "success": False,
                    "error": f"不支持的城市: {city}"
                }
            
            # 生成模拟预报数据
            base_weather = self.weather_data[city]
            forecast = []
            
            for i in range(days):
                day_weather = {
                    "date": time.strftime("%Y-%m-%d", time.localtime(time.time() + i * 86400)),
                    "temperature_high": base_weather["temperature"] + random.randint(-5, 5),
                    "temperature_low": base_weather["temperature"] + random.randint(-10, 0),
                    "humidity": base_weather["humidity"] + random.randint(-15, 15),
                    "condition": random.choice(["晴天", "多云", "阴天", "小雨", "中雨"])
                }
                forecast.append(day_weather)
            
            return {
                "success": True,
                "city": city,
                "forecast": forecast,
                "days": days
            }
            
        except Exception as e:
            self.logger.error(f"获取预报失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def search_cities(self, query: str) -> Dict[str, Any]:
        """搜索城市"""
        try:
            query = query.lower()
            matching_cities = [
                city for city in self.weather_data.keys()
                if query in city.lower()
            ]
            
            return {
                "success": True,
                "query": query,
                "cities": matching_cities,
                "count": len(matching_cities)
            }
            
        except Exception as e:
            self.logger.error(f"搜索城市失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    # 命令方法
    async def weather_command(self, args: str = "") -> Dict[str, Any]:
        """天气命令"""
        parts = args.strip().split() if args.strip() else []
        city = parts[0] if parts else None
        
        return await self.get_weather(city)
    
    async def forecast_command(self, args: str = "") -> Dict[str, Any]:
        """预报命令"""
        parts = args.strip().split() if args.strip() else []
        city = parts[0] if parts else None
        days = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else 3
        
        return await self.get_forecast(city, days)
    
    # 钩子方法
    async def on_user_message(self, message: str, **kwargs) -> Dict[str, Any]:
        """用户消息钩子"""
        # 检测天气相关关键词
        weather_keywords = ["天气", "weather", "温度", "湿度", "预报"]
        
        if any(keyword in message.lower() for keyword in weather_keywords):
            # 尝试提取城市名
            for city in self.weather_data.keys():
                if city in message:
                    self.logger.info(f"检测到天气查询: {city}")
                    return await self.get_weather(city)
        
        return {"handled": False}


class CalculatorPlugin(BasePlugin):
    """计算器插件示例"""
    
    def __init__(self, name: str, version: str, description: str):
        super().__init__(name, version, description)
        
        self.info.author = "UglyAgent Team"
        self.info.license = "MIT"
        self.info.tags = ["calculator", "math", "utility"]
        
        self.calculation_history = []
    
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """初始化插件"""
        self.logger.info("Calculator Plugin 初始化")
        return True
    
    async def activate(self) -> bool:
        """激活插件"""
        # 注册工具
        self.register_tool("calculate", self.calculate, "执行数学计算")
        self.register_tool("get_history", self.get_history, "获取计算历史")
        self.register_tool("clear_history", self.clear_history, "清除计算历史")
        
        # 注册命令
        self.register_command("calc", self.calc_command, "计算命令")
        self.register_command("history", self.history_command, "历史命令")
        
        return True
    
    async def deactivate(self) -> bool:
        """停用插件"""
        self.tools.clear()
        self.commands.clear()
        return True
    
    async def cleanup(self) -> bool:
        """清理插件资源"""
        self.calculation_history.clear()
        return True
    
    async def calculate(self, expression: str) -> Dict[str, Any]:
        """执行数学计算"""
        try:
            # 安全的数学表达式计算
            allowed_chars = set("0123456789+-*/.() ")
            if not all(c in allowed_chars for c in expression):
                return {
                    "success": False,
                    "error": "表达式包含不允许的字符"
                }
            
            result = eval(expression)
            
            # 记录历史
            calculation = {
                "expression": expression,
                "result": result,
                "timestamp": time.time()
            }
            self.calculation_history.append(calculation)
            
            # 限制历史记录数量
            if len(self.calculation_history) > 100:
                self.calculation_history.pop(0)
            
            return {
                "success": True,
                "expression": expression,
                "result": result,
                "history_count": len(self.calculation_history)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"计算错误: {str(e)}"
            }
    
    async def get_history(self, limit: int = 10) -> Dict[str, Any]:
        """获取计算历史"""
        recent_history = self.calculation_history[-limit:] if limit > 0 else self.calculation_history
        
        return {
            "success": True,
            "history": recent_history,
            "total_count": len(self.calculation_history)
        }
    
    async def clear_history(self) -> Dict[str, Any]:
        """清除计算历史"""
        count = len(self.calculation_history)
        self.calculation_history.clear()
        
        return {
            "success": True,
            "cleared_count": count
        }
    
    async def calc_command(self, args: str = "") -> Dict[str, Any]:
        """计算命令"""
        if not args.strip():
            return {
                "success": False,
                "error": "请提供计算表达式"
            }
        
        return await self.calculate(args.strip())
    
    async def history_command(self, args: str = "") -> Dict[str, Any]:
        """历史命令"""
        limit = 10
        if args.strip().isdigit():
            limit = int(args.strip())
        
        return await self.get_history(limit)


async def create_plugin_manifest(plugin_name: str, plugin_class: str, plugin_info: Dict[str, Any]):
    """创建插件清单文件"""
    manifest = {
        "info": plugin_info,
        "entry_point": f"{plugin_name}.{plugin_class}",
        "config_schema": {
            "type": "object",
            "properties": {}
        },
        "permissions": [
            "read_config",
            "write_logs",
            "register_tools",
            "register_commands",
            "register_hooks"
        ]
    }
    
    # 保存清单文件
    plugin_dir = Path(f"plugins/{plugin_name}")
    plugin_dir.mkdir(parents=True, exist_ok=True)
    
    manifest_file = plugin_dir / "plugin.json"
    with open(manifest_file, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 插件清单已创建: {manifest_file}")


async def test_weather_plugin():
    """测试天气插件"""
    print("🌤️ 测试天气插件")
    print("=" * 50)
    
    # 创建插件实例
    plugin = WeatherPlugin("weather_plugin", "1.0.0", "天气查询插件")
    
    # 初始化和激活
    await plugin.initialize()
    await plugin.activate()
    
    # 测试工具
    print("测试获取天气...")
    result = await plugin.get_weather("北京")
    print(f"北京天气: {result}")
    
    print("\n测试天气预报...")
    result = await plugin.get_forecast("上海", 5)
    print(f"上海预报: {result['success']}, {len(result.get('forecast', []))} 天")
    
    print("\n测试城市搜索...")
    result = await plugin.search_cities("京")
    print(f"搜索结果: {result}")
    
    # 测试命令
    print("\n测试天气命令...")
    result = await plugin.weather_command("广州")
    print(f"命令结果: {result['success']}")
    
    # 测试钩子
    print("\n测试消息钩子...")
    result = await plugin.on_user_message("今天深圳的天气怎么样？")
    print(f"钩子响应: {result.get('success', False)}")
    
    # 停用和清理
    await plugin.deactivate()
    await plugin.cleanup()
    
    print("✅ 天气插件测试完成")


async def test_calculator_plugin():
    """测试计算器插件"""
    print("\n🧮 测试计算器插件")
    print("=" * 50)
    
    # 创建插件实例
    plugin = CalculatorPlugin("calculator_plugin", "1.0.0", "数学计算插件")
    
    # 初始化和激活
    await plugin.initialize()
    await plugin.activate()
    
    # 测试计算
    expressions = ["2 + 3", "10 * 5", "100 / 4", "(2 + 3) * 4"]
    
    for expr in expressions:
        result = await plugin.calculate(expr)
        print(f"{expr} = {result.get('result', 'Error')}")
    
    # 测试历史
    print("\n计算历史:")
    history = await plugin.get_history(3)
    for calc in history["history"]:
        print(f"  {calc['expression']} = {calc['result']}")
    
    # 测试命令
    print("\n测试计算命令...")
    result = await plugin.calc_command("2 ** 8")
    print(f"2^8 = {result.get('result', 'Error')}")
    
    # 清理历史
    result = await plugin.clear_history()
    print(f"清除了 {result['cleared_count']} 条历史记录")
    
    # 停用和清理
    await plugin.deactivate()
    await plugin.cleanup()
    
    print("✅ 计算器插件测试完成")


async def test_plugin_manager():
    """测试插件管理器"""
    print("\n🔌 测试插件管理器")
    print("=" * 50)
    
    # 创建插件管理器
    manager = PluginManager(plugin_dirs=["plugins"])
    
    # 创建测试插件清单
    await create_plugin_manifest("weather_plugin", "WeatherPlugin", {
        "name": "weather_plugin",
        "version": "1.0.0",
        "description": "天气查询插件",
        "author": "UglyAgent Team"
    })
    
    # 发现和加载插件
    count = await manager.discover_and_load_plugins()
    print(f"加载了 {count} 个插件")
    
    # 获取插件状态
    status = manager.get_plugin_status()
    print(f"插件状态: {status['total_plugins']} 总数, {status['active_plugins']} 激活")
    
    # 列出可用工具
    tools = manager.get_available_tools()
    print(f"可用工具: {len(tools)} 个")
    for tool in tools[:5]:  # 只显示前5个
        print(f"  - {tool}")
    
    # 清理
    await manager.cleanup()
    
    print("✅ 插件管理器测试完成")


async def main():
    """主函数"""
    print("🔌 UglyAgent 插件开发示例")
    print("=" * 80)
    
    try:
        # 测试各个插件
        await test_weather_plugin()
        await test_calculator_plugin()
        await test_plugin_manager()
        
        print("\n🎉 所有插件测试完成！")
        
        print("\n💡 插件开发提示:")
        print("1. 继承 BasePlugin 类")
        print("2. 实现必需的生命周期方法")
        print("3. 注册工具、命令和钩子")
        print("4. 创建 plugin.json 清单文件")
        print("5. 处理错误和异常")
        print("6. 添加日志和调试信息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 运行失败: {e}")
        print("请检查依赖和配置是否正确")
