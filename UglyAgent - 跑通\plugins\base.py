#!/usr/bin/env python3
"""
UglyAgent 插件基类
定义插件的标准接口和生命周期管理
"""

import json
import importlib
import inspect
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from pathlib import Path
import sys

logger = logging.getLogger(__name__)


class PluginStatus(Enum):
    """插件状态"""
    UNLOADED = "unloaded"      # 未加载
    LOADED = "loaded"          # 已加载
    ACTIVE = "active"          # 激活中
    INACTIVE = "inactive"      # 未激活
    ERROR = "error"            # 错误状态


@dataclass
class PluginInfo:
    """插件信息"""
    name: str
    version: str
    description: str
    author: str = ""
    license: str = ""
    homepage: str = ""
    dependencies: List[str] = None
    python_requires: str = ">=3.7"
    tags: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.tags is None:
            self.tags = []


@dataclass
class PluginManifest:
    """插件清单"""
    info: PluginInfo
    entry_point: str
    config_schema: Dict[str, Any] = None
    permissions: List[str] = None
    
    def __post_init__(self):
        if self.config_schema is None:
            self.config_schema = {}
        if self.permissions is None:
            self.permissions = []


class BasePlugin(ABC):
    """插件基类"""
    
    def __init__(self, name: str, version: str, description: str):
        self.info = PluginInfo(
            name=name,
            version=version,
            description=description
        )
        self.status = PluginStatus.UNLOADED
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger(f"plugin.{name}")
        self.enabled = True
        
        # 插件提供的功能
        self.tools: Dict[str, Callable] = {}
        self.commands: Dict[str, Callable] = {}
        self.hooks: Dict[str, List[Callable]] = {}
        self.resources: Dict[str, Any] = {}
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """初始化插件"""
        pass
    
    @abstractmethod
    async def activate(self) -> bool:
        """激活插件"""
        pass
    
    @abstractmethod
    async def deactivate(self) -> bool:
        """停用插件"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """清理插件资源"""
        pass
    
    def get_info(self) -> PluginInfo:
        """获取插件信息"""
        return self.info
    
    def get_tools(self) -> Dict[str, Callable]:
        """获取插件提供的工具"""
        return self.tools.copy()
    
    def get_commands(self) -> Dict[str, Callable]:
        """获取插件提供的命令"""
        return self.commands.copy()
    
    def get_hooks(self) -> Dict[str, List[Callable]]:
        """获取插件提供的钩子"""
        return self.hooks.copy()
    
    def get_resources(self) -> Dict[str, Any]:
        """获取插件提供的资源"""
        return self.resources.copy()
    
    def register_tool(self, name: str, func: Callable, description: str = ""):
        """注册工具"""
        self.tools[name] = func
        self.logger.info(f"注册工具: {name}")
    
    def register_command(self, name: str, func: Callable, description: str = ""):
        """注册命令"""
        self.commands[name] = func
        self.logger.info(f"注册命令: {name}")
    
    def register_hook(self, event: str, func: Callable):
        """注册钩子"""
        if event not in self.hooks:
            self.hooks[event] = []
        self.hooks[event].append(func)
        self.logger.info(f"注册钩子: {event}")
    
    def register_resource(self, name: str, resource: Any):
        """注册资源"""
        self.resources[name] = resource
        self.logger.info(f"注册资源: {name}")
    
    def configure(self, config: Dict[str, Any]):
        """配置插件"""
        self.config.update(config)
        self.logger.info("插件配置已更新")
    
    def get_config(self, key: str = None, default: Any = None) -> Any:
        """获取配置"""
        if key is None:
            return self.config.copy()
        return self.config.get(key, default)
    
    def set_status(self, status: PluginStatus):
        """设置插件状态"""
        old_status = self.status
        self.status = status
        self.logger.info(f"插件状态变更: {old_status.value} -> {status.value}")
    
    def is_active(self) -> bool:
        """检查插件是否激活"""
        return self.status == PluginStatus.ACTIVE
    
    def validate_dependencies(self, available_plugins: List[str]) -> bool:
        """验证依赖"""
        for dep in self.info.dependencies:
            if dep not in available_plugins:
                self.logger.error(f"缺少依赖插件: {dep}")
                return False
        return True


class PluginLoader:
    """插件加载器"""
    
    def __init__(self, plugin_dirs: List[str] = None):
        self.plugin_dirs = plugin_dirs or ["plugins"]
        self.loaded_plugins: Dict[str, BasePlugin] = {}
        self.plugin_manifests: Dict[str, PluginManifest] = {}
        self.plugin_paths: Dict[str, Path] = {}
    
    def discover_plugins(self) -> List[str]:
        """发现插件"""
        discovered = []
        
        for plugin_dir in self.plugin_dirs:
            plugin_path = Path(plugin_dir)
            if not plugin_path.exists():
                continue
            
            # 查找插件目录
            for item in plugin_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    manifest_file = item / "plugin.json"
                    if manifest_file.exists():
                        try:
                            manifest = self._load_manifest(manifest_file)
                            self.plugin_manifests[manifest.info.name] = manifest
                            self.plugin_paths[manifest.info.name] = item
                            discovered.append(manifest.info.name)
                            logger.info(f"发现插件: {manifest.info.name}")
                        except Exception as e:
                            logger.error(f"加载插件清单失败 {item}: {e}")
        
        return discovered
    
    def _load_manifest(self, manifest_file: Path) -> PluginManifest:
        """加载插件清单"""
        with open(manifest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        info = PluginInfo(**data["info"])
        
        return PluginManifest(
            info=info,
            entry_point=data["entry_point"],
            config_schema=data.get("config_schema", {}),
            permissions=data.get("permissions", [])
        )
    
    async def load_plugin(self, plugin_name: str) -> bool:
        """加载插件"""
        try:
            if plugin_name in self.loaded_plugins:
                logger.warning(f"插件已加载: {plugin_name}")
                return True
            
            if plugin_name not in self.plugin_manifests:
                logger.error(f"未找到插件清单: {plugin_name}")
                return False
            
            manifest = self.plugin_manifests[plugin_name]
            plugin_path = self.plugin_paths[plugin_name]
            
            # 添加插件路径到Python路径
            if str(plugin_path) not in sys.path:
                sys.path.insert(0, str(plugin_path))
            
            # 动态导入插件模块
            module_name = manifest.entry_point.split('.')[0]
            class_name = manifest.entry_point.split('.')[1]
            
            module = importlib.import_module(module_name)
            plugin_class = getattr(module, class_name)
            
            # 验证插件类
            if not issubclass(plugin_class, BasePlugin):
                logger.error(f"插件类必须继承自BasePlugin: {plugin_name}")
                return False
            
            # 创建插件实例
            plugin_instance = plugin_class(
                manifest.info.name,
                manifest.info.version,
                manifest.info.description
            )
            
            plugin_instance.info = manifest.info
            plugin_instance.set_status(PluginStatus.LOADED)
            
            self.loaded_plugins[plugin_name] = plugin_instance
            
            logger.info(f"插件加载成功: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"加载插件失败 {plugin_name}: {e}")
            return False
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件"""
        try:
            if plugin_name not in self.loaded_plugins:
                logger.warning(f"插件未加载: {plugin_name}")
                return True
            
            plugin = self.loaded_plugins[plugin_name]
            
            # 停用插件
            if plugin.is_active():
                await plugin.deactivate()
            
            # 清理插件
            await plugin.cleanup()
            
            # 从加载列表移除
            del self.loaded_plugins[plugin_name]
            
            plugin.set_status(PluginStatus.UNLOADED)
            
            logger.info(f"插件卸载成功: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"卸载插件失败 {plugin_name}: {e}")
            return False
    
    async def activate_plugin(self, plugin_name: str, config: Dict[str, Any] = None) -> bool:
        """激活插件"""
        try:
            if plugin_name not in self.loaded_plugins:
                # 尝试加载插件
                if not await self.load_plugin(plugin_name):
                    return False
            
            plugin = self.loaded_plugins[plugin_name]
            
            if plugin.is_active():
                logger.warning(f"插件已激活: {plugin_name}")
                return True
            
            # 验证依赖
            available_plugins = list(self.loaded_plugins.keys())
            if not plugin.validate_dependencies(available_plugins):
                logger.error(f"插件依赖验证失败: {plugin_name}")
                return False
            
            # 初始化插件
            if not await plugin.initialize(config or {}):
                logger.error(f"插件初始化失败: {plugin_name}")
                return False
            
            # 激活插件
            if not await plugin.activate():
                logger.error(f"插件激活失败: {plugin_name}")
                return False
            
            plugin.set_status(PluginStatus.ACTIVE)
            
            logger.info(f"插件激活成功: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"激活插件失败 {plugin_name}: {e}")
            return False
    
    async def deactivate_plugin(self, plugin_name: str) -> bool:
        """停用插件"""
        try:
            if plugin_name not in self.loaded_plugins:
                logger.warning(f"插件未加载: {plugin_name}")
                return True
            
            plugin = self.loaded_plugins[plugin_name]
            
            if not plugin.is_active():
                logger.warning(f"插件未激活: {plugin_name}")
                return True
            
            # 停用插件
            if not await plugin.deactivate():
                logger.error(f"插件停用失败: {plugin_name}")
                return False
            
            plugin.set_status(PluginStatus.INACTIVE)
            
            logger.info(f"插件停用成功: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"停用插件失败 {plugin_name}: {e}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """获取插件实例"""
        return self.loaded_plugins.get(plugin_name)
    
    def get_active_plugins(self) -> List[BasePlugin]:
        """获取激活的插件"""
        return [
            plugin for plugin in self.loaded_plugins.values()
            if plugin.is_active()
        ]
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """获取插件信息"""
        if plugin_name in self.loaded_plugins:
            return self.loaded_plugins[plugin_name].get_info()
        elif plugin_name in self.plugin_manifests:
            return self.plugin_manifests[plugin_name].info
        return None
    
    def list_plugins(self) -> Dict[str, Dict[str, Any]]:
        """列出所有插件"""
        result = {}
        
        # 已发现的插件
        for name, manifest in self.plugin_manifests.items():
            result[name] = {
                "info": asdict(manifest.info),
                "status": PluginStatus.UNLOADED.value,
                "loaded": False,
                "active": False
            }
        
        # 已加载的插件
        for name, plugin in self.loaded_plugins.items():
            if name in result:
                result[name]["status"] = plugin.status.value
                result[name]["loaded"] = True
                result[name]["active"] = plugin.is_active()
            else:
                result[name] = {
                    "info": asdict(plugin.get_info()),
                    "status": plugin.status.value,
                    "loaded": True,
                    "active": plugin.is_active()
                }
        
        return result
    
    async def reload_plugin(self, plugin_name: str) -> bool:
        """重新加载插件"""
        try:
            # 先卸载
            await self.unload_plugin(plugin_name)
            
            # 重新发现插件
            self.discover_plugins()
            
            # 重新加载
            return await self.load_plugin(plugin_name)
            
        except Exception as e:
            logger.error(f"重新加载插件失败 {plugin_name}: {e}")
            return False
