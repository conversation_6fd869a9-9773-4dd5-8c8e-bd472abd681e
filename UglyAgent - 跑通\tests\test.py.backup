```python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n-1):
        swapped = False
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
                swapped = True
        if not swapped:
            break
    return arr

if __name__ == "__main__":
    test_cases = [
        [64, 34, 25, 12, 22, 11, 90],
        [5, 1, 4, 2],
        [10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
        [],
        [1]
    ]
    
    for case in test_cases:
        print(f"Original: {case}")
        sorted_arr = bubble_sort(case.copy())
        print(f"Sorted:   {sorted_arr}\n")
```