#!/usr/bin/env python3
"""
UglyAgent MCP服务器
增强版MCP协议服务器，支持更多功能和工具
"""

import asyncio
import json
import sys
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import signal

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mcp.protocol import MCPProtocolHandler, ToolDefinition, ResourceDefinition, PromptDefinition
from tools.file_ops import FileOperationTools
from tools.code_analysis import CodeAnalysisTools
from tools.web_tools import WebTools
from tools.system_tools import SystemTools
from core.llm_manager import LLMManager
from config.settings import get_config, get_mcp_settings
from utils.logger import setup_logger

logger = logging.getLogger(__name__)


class UglyAgentMCPServer:
    """UglyAgent MCP服务器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = get_config()
        self.mcp_settings = get_mcp_settings()
        self.protocol_handler = MCPProtocolHandler()
        self.llm_manager: Optional[LLMManager] = None
        self.running = False
        self.server = None
        
        # 工具实例
        self.file_tools = FileOperationTools()
        self.code_tools = CodeAnalysisTools()
        self.web_tools = WebTools()
        self.system_tools = SystemTools()
        
        # 初始化
        self._setup_server_info()
        self._register_tools()
        self._register_resources()
        self._register_prompts()
        self._setup_signal_handlers()
    
    def _setup_server_info(self):
        """设置服务器信息"""
        agent_settings = self.config.agent_settings
        self.protocol_handler.server_info = {
            "name": agent_settings.name,
            "version": agent_settings.version,
            "description": agent_settings.description
        }
        
        # 设置能力
        self.protocol_handler.capabilities = {
            "tools": {},
            "resources": {},
            "prompts": {},
            "completion": {"argumentHints": True},
            "logging": {}
        }
    
    def _register_tools(self):
        """注册所有工具"""
        # 文件操作工具
        file_tools = [
            ToolDefinition(
                name="read_file",
                description="读取文件内容，支持多种文件格式",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "文件路径"},
                        "encoding": {"type": "string", "description": "文件编码", "default": "utf-8"}
                    },
                    "required": ["file_path"]
                }
            ),
            ToolDefinition(
                name="write_file",
                description="写入文件内容",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "文件路径"},
                        "content": {"type": "string", "description": "文件内容"},
                        "encoding": {"type": "string", "description": "文件编码", "default": "utf-8"}
                    },
                    "required": ["file_path", "content"]
                }
            ),
            ToolDefinition(
                name="list_directory",
                description="列出目录内容",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "directory_path": {"type": "string", "description": "目录路径"},
                        "recursive": {"type": "boolean", "description": "是否递归", "default": False}
                    },
                    "required": ["directory_path"]
                }
            ),
            ToolDefinition(
                name="search_files",
                description="搜索文件内容",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "pattern": {"type": "string", "description": "搜索模式"},
                        "directory": {"type": "string", "description": "搜索目录"},
                        "file_types": {"type": "array", "items": {"type": "string"}, "description": "文件类型"}
                    },
                    "required": ["pattern", "directory"]
                }
            )
        ]
        
        for tool in file_tools:
            self.protocol_handler.register_tool(tool, getattr(self.file_tools, tool.name))
        
        # 代码分析工具
        code_tools = [
            ToolDefinition(
                name="analyze_code",
                description="分析代码结构和复杂度",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "代码文件路径"},
                        "language": {"type": "string", "description": "编程语言"}
                    },
                    "required": ["file_path"]
                }
            ),
            ToolDefinition(
                name="extract_functions",
                description="提取代码中的函数定义",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "代码文件路径"}
                    },
                    "required": ["file_path"]
                }
            ),
            ToolDefinition(
                name="check_syntax",
                description="检查代码语法",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "代码文件路径"},
                        "language": {"type": "string", "description": "编程语言"}
                    },
                    "required": ["file_path"]
                }
            )
        ]
        
        for tool in code_tools:
            self.protocol_handler.register_tool(tool, getattr(self.code_tools, tool.name))
        
        # Web工具
        web_tools = [
            ToolDefinition(
                name="http_request",
                description="发送HTTP请求",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "url": {"type": "string", "description": "请求URL"},
                        "method": {"type": "string", "description": "HTTP方法", "default": "GET"},
                        "headers": {"type": "object", "description": "请求头"},
                        "data": {"type": "object", "description": "请求数据"}
                    },
                    "required": ["url"]
                }
            ),
            ToolDefinition(
                name="scrape_webpage",
                description="抓取网页内容",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "url": {"type": "string", "description": "网页URL"},
                        "selector": {"type": "string", "description": "CSS选择器"}
                    },
                    "required": ["url"]
                }
            )
        ]
        
        for tool in web_tools:
            self.protocol_handler.register_tool(tool, getattr(self.web_tools, tool.name))
        
        # 系统工具
        system_tools = [
            ToolDefinition(
                name="execute_command",
                description="执行系统命令",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "command": {"type": "string", "description": "要执行的命令"},
                        "working_directory": {"type": "string", "description": "工作目录"},
                        "timeout": {"type": "integer", "description": "超时时间(秒)", "default": 30}
                    },
                    "required": ["command"]
                }
            ),
            ToolDefinition(
                name="get_system_info",
                description="获取系统信息",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "info_type": {"type": "string", "description": "信息类型", "enum": ["cpu", "memory", "disk", "network", "all"]}
                    }
                }
            )
        ]
        
        for tool in system_tools:
            self.protocol_handler.register_tool(tool, getattr(self.system_tools, tool.name))
        
        # AI对话工具
        ai_tool = ToolDefinition(
            name="ai_chat",
            description="与AI模型对话",
            inputSchema={
                "type": "object",
                "properties": {
                    "message": {"type": "string", "description": "对话消息"},
                    "model": {"type": "string", "description": "使用的模型"},
                    "system_prompt": {"type": "string", "description": "系统提示"}
                },
                "required": ["message"]
            }
        )
        self.protocol_handler.register_tool(ai_tool, self._handle_ai_chat)
    
    def _register_resources(self):
        """注册资源"""
        # 配置资源
        config_resource = ResourceDefinition(
            uri="config://settings",
            name="Agent配置",
            description="UglyAgent配置信息",
            mimeType="application/json"
        )
        self.protocol_handler.register_resource(config_resource, self._get_config_resource)
        
        # 工具列表资源
        tools_resource = ResourceDefinition(
            uri="tools://list",
            name="工具列表",
            description="可用工具列表",
            mimeType="application/json"
        )
        self.protocol_handler.register_resource(tools_resource, self._get_tools_resource)
    
    def _register_prompts(self):
        """注册提示"""
        # 代码分析提示
        code_analysis_prompt = PromptDefinition(
            name="code_analysis",
            description="代码分析提示模板",
            arguments=[
                {"name": "file_path", "description": "代码文件路径", "required": True},
                {"name": "analysis_type", "description": "分析类型", "required": False}
            ]
        )
        self.protocol_handler.register_prompt(code_analysis_prompt, self._get_code_analysis_prompt)
        
        # 文档生成提示
        doc_generation_prompt = PromptDefinition(
            name="doc_generation",
            description="文档生成提示模板",
            arguments=[
                {"name": "code_content", "description": "代码内容", "required": True},
                {"name": "doc_type", "description": "文档类型", "required": False}
            ]
        )
        self.protocol_handler.register_prompt(doc_generation_prompt, self._get_doc_generation_prompt)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在关闭服务器...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _handle_ai_chat(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理AI对话"""
        try:
            if not self.llm_manager:
                self.llm_manager = LLMManager()
                await self.llm_manager.__aenter__()
            
            message = params.get("message", "")
            model = params.get("model")
            system_prompt = params.get("system_prompt")
            
            response = await self.llm_manager.chat(message, model, system_prompt)
            
            return {
                "success": True,
                "response": response.content,
                "model": response.model,
                "tokens_used": response.tokens_used,
                "cost": response.cost,
                "response_time": response.response_time
            }
            
        except Exception as e:
            logger.error(f"AI对话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_config_resource(self, params: Dict[str, Any]) -> str:
        """获取配置资源"""
        config_dict = self.config.get_config_dict()
        return json.dumps(config_dict, indent=2, ensure_ascii=False)
    
    async def _get_tools_resource(self, params: Dict[str, Any]) -> str:
        """获取工具列表资源"""
        tools_info = {
            "tools": list(self.protocol_handler.tools.keys()),
            "count": len(self.protocol_handler.tools)
        }
        return json.dumps(tools_info, indent=2, ensure_ascii=False)
    
    async def _get_code_analysis_prompt(self, params: Dict[str, Any]) -> str:
        """获取代码分析提示"""
        file_path = params.get("file_path", "")
        analysis_type = params.get("analysis_type", "comprehensive")
        
        return f"""请分析以下代码文件: {file_path}

分析类型: {analysis_type}

请提供以下分析:
1. 代码结构和组织
2. 复杂度分析
3. 潜在问题和改进建议
4. 代码质量评估

请详细分析并提供具体的改进建议。"""
    
    async def _get_doc_generation_prompt(self, params: Dict[str, Any]) -> str:
        """获取文档生成提示"""
        code_content = params.get("code_content", "")
        doc_type = params.get("doc_type", "api")
        
        return f"""请为以下代码生成{doc_type}文档:

{code_content}

请包含:
1. 功能描述
2. 参数说明
3. 返回值说明
4. 使用示例
5. 注意事项

请生成清晰、详细的文档。"""

    async def start_stdio_server(self):
        """启动标准输入输出服务器"""
        logger.info("启动UglyAgent MCP服务器 (stdio模式)")
        self.running = True

        try:
            while self.running:
                # 从标准输入读取请求
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )

                if not line:
                    break

                line = line.strip()
                if not line:
                    continue

                # 处理消息
                response = await self.protocol_handler.handle_message(line)

                if response:
                    # 发送响应到标准输出
                    print(response)
                    sys.stdout.flush()

        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭服务器...")
        except Exception as e:
            logger.error(f"服务器错误: {e}")
        finally:
            await self.stop()

    async def start_tcp_server(self, host: str = None, port: int = None):
        """启动TCP服务器"""
        if host is None:
            host = self.mcp_settings.host
        if port is None:
            port = self.mcp_settings.port

        logger.info(f"启动UglyAgent MCP服务器 (TCP模式) - {host}:{port}")

        async def handle_client(reader, writer):
            """处理客户端连接"""
            client_addr = writer.get_extra_info('peername')
            logger.info(f"客户端连接: {client_addr}")

            try:
                while True:
                    # 读取消息
                    data = await reader.readline()
                    if not data:
                        break

                    message = data.decode('utf-8').strip()
                    if not message:
                        continue

                    # 处理消息
                    response = await self.protocol_handler.handle_message(message)

                    if response:
                        # 发送响应
                        writer.write((response + '\n').encode('utf-8'))
                        await writer.drain()

            except Exception as e:
                logger.error(f"处理客户端 {client_addr} 时出错: {e}")
            finally:
                writer.close()
                await writer.wait_closed()
                logger.info(f"客户端断开连接: {client_addr}")

        self.server = await asyncio.start_server(
            handle_client, host, port
        )

        self.running = True

        async with self.server:
            await self.server.serve_forever()

    async def stop(self):
        """停止服务器"""
        logger.info("正在停止UglyAgent MCP服务器...")
        self.running = False

        if self.server:
            self.server.close()
            await self.server.wait_closed()

        if self.llm_manager:
            await self.llm_manager.__aexit__(None, None, None)

        logger.info("服务器已停止")

    def get_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        return {
            "running": self.running,
            "protocol_status": self.protocol_handler.get_status(),
            "tools_count": len(self.protocol_handler.tools),
            "resources_count": len(self.protocol_handler.resources),
            "prompts_count": len(self.protocol_handler.prompts)
        }


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="UglyAgent MCP服务器")
    parser.add_argument("--mode", choices=["stdio", "tcp"], default="stdio",
                       help="服务器模式 (默认: stdio)")
    parser.add_argument("--host", default="localhost",
                       help="TCP服务器主机 (默认: localhost)")
    parser.add_argument("--port", type=int, default=8080,
                       help="TCP服务器端口 (默认: 8080)")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--debug", action="store_true",
                       help="启用调试模式")

    args = parser.parse_args()

    # 设置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logger(level=log_level)

    # 创建服务器
    server = UglyAgentMCPServer(args.config)

    try:
        if args.mode == "stdio":
            await server.start_stdio_server()
        else:
            await server.start_tcp_server(args.host, args.port)
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
    finally:
        await server.stop()


if __name__ == "__main__":
    asyncio.run(main())
