#!/bin/bash
# UglyAgent 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示Logo
show_logo() {
    echo -e "${PURPLE}"
    echo "██╗   ██╗ ██████╗ ██╗  ██╗   ██╗ █████╗  ██████╗ ███████╗███╗   ██╗████████╗"
    echo "██║   ██║██╔════╝ ██║  ╚██╗ ██╔╝██╔══██╗██╔════╝ ██╔════╝████╗  ██║╚══██╔══╝"
    echo "██║   ██║██║  ███╗██║   ╚████╔╝ ███████║██║  ███╗█████╗  ██╔██╗ ██║   ██║   "
    echo "██║   ██║██║   ██║██║    ╚██╔╝  ██╔══██║██║   ██║██╔══╝  ██║╚██╗██║   ██║   "
    echo "╚██████╔╝╚██████╔╝███████╗██║   ██║  ██║╚██████╔╝███████╗██║ ╚████║   ██║   "
    echo " ╚═════╝  ╚═════╝ ╚══════╝╚═╝   ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝   ╚═╝   "
    echo -e "${NC}"
    echo -e "${CYAN}超越Augment的AI Agent${NC}"
    echo -e "${YELLOW}版本: 1.0.0${NC}"
    echo ""
}

# 检查虚拟环境
check_venv() {
    if [ -d "venv" ]; then
        echo -e "${GREEN}✅ 发现虚拟环境${NC}"
        
        # 激活虚拟环境
        if [ -f "venv/bin/activate" ]; then
            source venv/bin/activate
            echo -e "${GREEN}✅ 虚拟环境已激活${NC}"
        elif [ -f "venv/Scripts/activate" ]; then
            source venv/Scripts/activate
            echo -e "${GREEN}✅ 虚拟环境已激活${NC}"
        else
            echo -e "${YELLOW}⚠️ 无法激活虚拟环境，使用系统Python${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 未发现虚拟环境，使用系统Python${NC}"
    fi
}

# 检查配置文件
check_config() {
    echo "检查配置文件..."
    
    if [ ! -f "config/models.yaml" ]; then
        if [ -f "config/models.yaml.example" ]; then
            echo -e "${YELLOW}⚠️ 模型配置文件不存在，正在创建...${NC}"
            cp config/models.yaml.example config/models.yaml
            echo -e "${RED}❗ 请编辑 config/models.yaml 添加您的API密钥${NC}"
            read -p "按Enter继续..."
        else
            echo -e "${RED}❌ 配置文件模板不存在${NC}"
            exit 1
        fi
    fi
    
    if [ ! -f "config/config.yaml" ]; then
        if [ -f "config/config.yaml.example" ]; then
            echo -e "${YELLOW}⚠️ 主配置文件不存在，正在创建...${NC}"
            cp config/config.yaml.example config/config.yaml
        fi
    fi
    
    echo -e "${GREEN}✅ 配置文件检查完成${NC}"
}

# 检查依赖
check_dependencies() {
    echo "检查依赖..."
    
    python -c "
import sys
try:
    import aiohttp
    import yaml
    import psutil
    print('✅ 核心依赖检查通过')
except ImportError as e:
    print(f'❌ 缺少依赖: {e}')
    print('请运行: pip install -r requirements.txt')
    sys.exit(1)
" || exit 1
}

# 显示启动菜单
show_menu() {
    echo -e "${BLUE}请选择启动模式:${NC}"
    echo "1) 🗣️  交互模式 (默认)"
    echo "2) 🔌 MCP服务器 (stdio)"
    echo "3) 🌐 MCP服务器 (TCP)"
    echo "4) 🚀 API服务器"
    echo "5) 🔧 配置向导"
    echo "6) 📊 系统状态"
    echo "7) 🧪 运行测试"
    echo "8) 📖 查看帮助"
    echo "9) 🚪 退出"
    echo ""
}

# 交互模式
start_interactive() {
    echo -e "${GREEN}🗣️ 启动交互模式...${NC}"
    python main.py interactive
}

# MCP stdio模式
start_mcp_stdio() {
    echo -e "${GREEN}🔌 启动MCP服务器 (stdio模式)...${NC}"
    python main.py mcp-stdio
}

# MCP TCP模式
start_mcp_tcp() {
    echo -e "${BLUE}🌐 启动MCP服务器 (TCP模式)${NC}"
    
    read -p "主机地址 (默认: localhost): " host
    host=${host:-localhost}
    
    read -p "端口 (默认: 8080): " port
    port=${port:-8080}
    
    echo -e "${GREEN}启动TCP服务器在 ${host}:${port}...${NC}"
    python main.py mcp-tcp --host "$host" --port "$port"
}

# API服务器模式
start_api_server() {
    echo -e "${BLUE}🚀 启动API服务器${NC}"
    
    read -p "主机地址 (默认: 0.0.0.0): " host
    host=${host:-0.0.0.0}
    
    read -p "端口 (默认: 8000): " port
    port=${port:-8000}
    
    echo -e "${GREEN}启动API服务器在 ${host}:${port}...${NC}"
    echo -e "${CYAN}API文档: http://${host}:${port}/docs${NC}"
    python main.py api --host "$host" --port "$port"
}

# 配置向导
config_wizard() {
    echo -e "${BLUE}🔧 配置向导${NC}"
    echo ""
    
    echo "1. 配置大模型API"
    echo "2. 配置插件"
    echo "3. 配置日志"
    echo "4. 查看当前配置"
    echo "5. 返回主菜单"
    
    read -p "请选择: " config_choice
    
    case $config_choice in
        1)
            echo -e "${YELLOW}正在打开模型配置文件...${NC}"
            ${EDITOR:-nano} config/models.yaml
            ;;
        2)
            echo -e "${YELLOW}正在打开插件配置...${NC}"
            ${EDITOR:-nano} plugins/config.json
            ;;
        3)
            echo -e "${YELLOW}正在打开主配置文件...${NC}"
            ${EDITOR:-nano} config/config.yaml
            ;;
        4)
            echo -e "${CYAN}当前配置:${NC}"
            python -c "
from UglyAgent.config.settings import get_config
import json
config = get_config()
print(json.dumps(config.get_config_dict(), indent=2, ensure_ascii=False))
"
            read -p "按Enter继续..."
            ;;
        5)
            return
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 系统状态
show_status() {
    echo -e "${BLUE}📊 系统状态${NC}"
    echo ""
    
    python -c "
import sys
import platform
import psutil
from pathlib import Path

print(f'Python版本: {sys.version}')
print(f'平台: {platform.platform()}')
print(f'CPU: {psutil.cpu_count()} 核心')
print(f'内存: {psutil.virtual_memory().total // (1024**3)} GB')
print(f'磁盘空间: {psutil.disk_usage(\".\").free // (1024**3)} GB 可用')
print()

# 检查配置文件
config_files = [
    'config/config.yaml',
    'config/models.yaml',
    'plugins/config.json'
]

print('配置文件状态:')
for file in config_files:
    if Path(file).exists():
        print(f'  ✅ {file}')
    else:
        print(f'  ❌ {file}')

print()

# 检查依赖
print('依赖检查:')
deps = ['aiohttp', 'yaml', 'psutil', 'beautifulsoup4', 'requests']
for dep in deps:
    try:
        __import__(dep)
        print(f'  ✅ {dep}')
    except ImportError:
        print(f'  ❌ {dep}')
"
    
    read -p "按Enter继续..."
}

# 运行测试
run_tests() {
    echo -e "${BLUE}🧪 运行测试${NC}"
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v
    else
        echo "运行基本测试..."
        python -c "
import sys
sys.path.insert(0, '.')

try:
    from UglyAgent.core.agent import UglyAgent
    from UglyAgent.config.settings import get_config
    from UglyAgent.mcp.server import UglyAgentMCPServer
    print('✅ 所有核心模块导入成功')
except Exception as e:
    print(f'❌ 模块导入失败: {e}')
    sys.exit(1)
"
    fi
    
    read -p "按Enter继续..."
}

# 显示帮助
show_help() {
    echo -e "${BLUE}📖 UglyAgent 帮助${NC}"
    echo ""
    echo "启动模式:"
    echo "  interactive  - 交互式对话模式"
    echo "  mcp-stdio    - MCP协议标准输入输出模式"
    echo "  mcp-tcp      - MCP协议TCP服务器模式"
    echo "  api          - HTTP API服务器模式"
    echo ""
    echo "配置文件:"
    echo "  config/config.yaml  - 主配置文件"
    echo "  config/models.yaml  - 大模型配置"
    echo "  plugins/config.json - 插件配置"
    echo ""
    echo "命令行选项:"
    echo "  --help       - 显示帮助"
    echo "  --log-level  - 设置日志级别"
    echo "  --config     - 指定配置文件"
    echo ""
    echo "更多信息请查看 README.md"
    
    read -p "按Enter继续..."
}

# 主循环
main_loop() {
    while true; do
        clear
        show_logo
        show_menu
        
        read -p "请选择 (1-9): " choice
        
        case $choice in
            1)
                start_interactive
                ;;
            2)
                start_mcp_stdio
                ;;
            3)
                start_mcp_tcp
                ;;
            4)
                start_api_server
                ;;
            5)
                config_wizard
                ;;
            6)
                show_status
                ;;
            7)
                run_tests
                ;;
            8)
                show_help
                ;;
            9)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                sleep 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -f "main.py" ]; then
        echo -e "${RED}❌ 请在UglyAgent根目录运行此脚本${NC}"
        exit 1
    fi
    
    check_venv
    check_config
    check_dependencies
    
    # 如果有命令行参数，直接执行
    if [ $# -gt 0 ]; then
        python main.py "$@"
    else
        main_loop
    fi
}

# 错误处理
trap 'echo -e "\n${RED}❌ 程序被中断${NC}"; exit 1' INT

# 运行主函数
main "$@"
