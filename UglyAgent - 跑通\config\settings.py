#!/usr/bin/env python3
"""
UglyAgent 配置管理系统
"""

import os
import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class AgentSettings:
    """Agent基础设置"""
    name: str = "UglyAgent"
    version: str = "1.0.0"
    description: str = "超越Augment的AI Agent"
    debug: bool = False
    log_level: str = "INFO"
    max_memory_items: int = 1000
    session_timeout: int = 3600  # 秒


@dataclass
class MCPSettings:
    """MCP协议设置"""
    enabled: bool = True
    host: str = "localhost"
    port: int = 8080
    protocol_version: str = "2024-11-05"
    max_connections: int = 100
    timeout: int = 30


@dataclass
class SecuritySettings:
    """安全设置"""
    encrypt_api_keys: bool = True
    log_requests: bool = False
    filter_sensitive_data: bool = True
    max_request_size: int = 10 * 1024 * 1024  # 10MB
    rate_limit_requests: int = 100  # 每分钟
    allowed_file_extensions: list = None
    
    def __post_init__(self):
        if self.allowed_file_extensions is None:
            self.allowed_file_extensions = ['.txt', '.py', '.js', '.json', '.yaml', '.md']


@dataclass
class PerformanceSettings:
    """性能设置"""
    max_concurrent_requests: int = 10
    request_queue_size: int = 1000
    cache_enabled: bool = True
    cache_ttl: int = 3600  # 秒
    cache_max_size: int = 1000
    worker_threads: int = 4


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.agent_settings = AgentSettings()
        self.mcp_settings = MCPSettings()
        self.security_settings = SecuritySettings()
        self.performance_settings = PerformanceSettings()
        
        # 加载配置
        self.load_all_configs()
    
    def load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 加载主配置
            main_config_path = self.config_dir / "config.yaml"
            if main_config_path.exists():
                self.load_main_config(main_config_path)
            
            # 加载环境变量配置
            self.load_env_config()
            
            logger.info("配置加载完成")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
    
    def load_main_config(self, config_path: Path):
        """加载主配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新各个设置
            if 'agent' in config:
                self._update_dataclass(self.agent_settings, config['agent'])
            
            if 'mcp' in config:
                self._update_dataclass(self.mcp_settings, config['mcp'])
            
            if 'security' in config:
                self._update_dataclass(self.security_settings, config['security'])
            
            if 'performance' in config:
                self._update_dataclass(self.performance_settings, config['performance'])
            
        except Exception as e:
            logger.error(f"加载主配置文件失败: {e}")
    
    def load_env_config(self):
        """从环境变量加载配置"""
        # Agent设置
        if os.getenv('UGLY_AGENT_DEBUG'):
            self.agent_settings.debug = os.getenv('UGLY_AGENT_DEBUG').lower() == 'true'
        
        if os.getenv('UGLY_AGENT_LOG_LEVEL'):
            self.agent_settings.log_level = os.getenv('UGLY_AGENT_LOG_LEVEL')
        
        # MCP设置
        if os.getenv('UGLY_AGENT_MCP_HOST'):
            self.mcp_settings.host = os.getenv('UGLY_AGENT_MCP_HOST')
        
        if os.getenv('UGLY_AGENT_MCP_PORT'):
            self.mcp_settings.port = int(os.getenv('UGLY_AGENT_MCP_PORT'))
        
        # 安全设置
        if os.getenv('UGLY_AGENT_ENCRYPT_KEYS'):
            self.security_settings.encrypt_api_keys = os.getenv('UGLY_AGENT_ENCRYPT_KEYS').lower() == 'true'
    
    def _update_dataclass(self, dataclass_instance, config_dict: Dict[str, Any]):
        """更新数据类实例"""
        for key, value in config_dict.items():
            if hasattr(dataclass_instance, key):
                setattr(dataclass_instance, key, value)
    
    def save_config(self, config_path: Optional[str] = None):
        """保存配置到文件"""
        if config_path is None:
            config_path = self.config_dir / "config.yaml"
        else:
            config_path = Path(config_path)
        
        config = {
            'agent': asdict(self.agent_settings),
            'mcp': asdict(self.mcp_settings),
            'security': asdict(self.security_settings),
            'performance': asdict(self.performance_settings)
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已保存到: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取完整配置字典"""
        return {
            'agent': asdict(self.agent_settings),
            'mcp': asdict(self.mcp_settings),
            'security': asdict(self.security_settings),
            'performance': asdict(self.performance_settings)
        }
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证端口范围
            if not (1 <= self.mcp_settings.port <= 65535):
                logger.error(f"无效的MCP端口: {self.mcp_settings.port}")
                return False
            
            # 验证日志级别
            valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if self.agent_settings.log_level not in valid_log_levels:
                logger.error(f"无效的日志级别: {self.agent_settings.log_level}")
                return False
            
            # 验证性能设置
            if self.performance_settings.max_concurrent_requests <= 0:
                logger.error("最大并发请求数必须大于0")
                return False
            
            if self.performance_settings.cache_ttl <= 0:
                logger.error("缓存TTL必须大于0")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def create_default_config(self):
        """创建默认配置文件"""
        config_path = self.config_dir / "config.yaml.example"
        
        example_config = {
            'agent': {
                'name': 'UglyAgent',
                'version': '1.0.0',
                'description': '超越Augment的AI Agent',
                'debug': False,
                'log_level': 'INFO',
                'max_memory_items': 1000,
                'session_timeout': 3600
            },
            'mcp': {
                'enabled': True,
                'host': 'localhost',
                'port': 8080,
                'protocol_version': '2024-11-05',
                'max_connections': 100,
                'timeout': 30
            },
            'security': {
                'encrypt_api_keys': True,
                'log_requests': False,
                'filter_sensitive_data': True,
                'max_request_size': 10485760,  # 10MB
                'rate_limit_requests': 100,
                'allowed_file_extensions': ['.txt', '.py', '.js', '.json', '.yaml', '.md']
            },
            'performance': {
                'max_concurrent_requests': 10,
                'request_queue_size': 1000,
                'cache_enabled': True,
                'cache_ttl': 3600,
                'cache_max_size': 1000,
                'worker_threads': 4
            }
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(example_config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"默认配置文件已创建: {config_path}")
            
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {e}")


# 全局配置实例
config_manager = ConfigManager()


def get_config() -> ConfigManager:
    """获取全局配置管理器"""
    return config_manager


def reload_config():
    """重新加载配置"""
    global config_manager
    config_manager.load_all_configs()


# 便捷访问函数
def get_agent_settings() -> AgentSettings:
    """获取Agent设置"""
    return config_manager.agent_settings


def get_mcp_settings() -> MCPSettings:
    """获取MCP设置"""
    return config_manager.mcp_settings


def get_security_settings() -> SecuritySettings:
    """获取安全设置"""
    return config_manager.security_settings


def get_performance_settings() -> PerformanceSettings:
    """获取性能设置"""
    return config_manager.performance_settings
