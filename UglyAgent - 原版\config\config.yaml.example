# UglyAgent 主配置文件示例
# 复制此文件为 config.yaml 并根据需要修改

# Agent基础配置
agent:
  name: "UglyAgent"
  version: "1.0.0"
  description: "超越Augment的AI Agent"
  debug: false
  log_level: "INFO"
  max_memory_items: 1000
  session_timeout: 3600  # 会话超时时间(秒)

# MCP协议配置
mcp:
  enabled: true
  host: "localhost"
  port: 8080
  protocol_version: "2024-11-05"
  max_connections: 100
  timeout: 30

# 安全配置
security:
  encrypt_api_keys: true
  log_requests: false
  filter_sensitive_data: true
  max_request_size: 10485760  # 10MB
  rate_limit_requests: 100  # 每分钟请求数
  allowed_file_extensions:
    - ".txt"
    - ".py"
    - ".js"
    - ".json"
    - ".yaml"
    - ".md"
    - ".html"
    - ".css"
    - ".xml"
    - ".csv"
    - ".log"

# 性能配置
performance:
  max_concurrent_requests: 10
  request_queue_size: 1000
  cache_enabled: true
  cache_ttl: 3600  # 缓存时间(秒)
  cache_max_size: 1000
  worker_threads: 4

# 日志配置
logging:
  level: "INFO"
  file: "logs/uglyagent.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
  console_output: true
  colored_output: true
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 插件配置
plugins:
  enabled: true
  auto_discover: true
  plugin_dirs:
    - "plugins"
    - "plugins/examples"
  security_enabled: true
  max_plugins: 50

# 工具配置
tools:
  file_operations:
    enabled: true
    max_file_size: *********  # 100MB
    safe_extensions:
      - ".txt"
      - ".py"
      - ".js"
      - ".json"
      - ".yaml"
      - ".md"
    backup_enabled: true

  code_analysis:
    enabled: true
    supported_languages:
      - "python"
      - "javascript"
      - "typescript"
      - "java"
      - "cpp"
      - "c"
      - "go"
      - "rust"
    syntax_check_enabled: true

  web_tools:
    enabled: true
    max_response_size: 10485760  # 10MB
    timeout: 30
    max_redirects: 10
    user_agent: "UglyAgent/1.0.0"

  system_tools:
    enabled: true
    safe_mode: true
    max_execution_time: 60
    allowed_commands:
      - "ls"
      - "dir"
      - "pwd"
      - "cat"
      - "echo"
      - "find"
      - "grep"
      - "ps"
      - "top"
      - "df"
      - "free"

# 记忆系统配置
memory:
  enabled: true
  database_path: "memory.db"
  max_short_term: 1000
  max_long_term: 10000
  consolidation_threshold: 0.7
  cleanup_days: 30
  min_importance: 0.3

# 任务规划配置
planning:
  enabled: true
  max_concurrent_tasks: 5
  max_task_depth: 10
  default_timeout: 300  # 任务超时时间(秒)
  max_retries: 3
  planning_templates:
    - "file_analysis"
    - "web_scraping"
    - "code_review"
    - "system_monitoring"

# API服务器配置
api_server:
  enabled: false
  host: "0.0.0.0"
  port: 8000
  cors_enabled: true
  cors_origins:
    - "*"
  rate_limiting:
    enabled: true
    requests_per_minute: 60
  authentication:
    enabled: false
    api_key: ""

# 监控配置
monitoring:
  enabled: true
  metrics_enabled: true
  health_check_interval: 60  # 健康检查间隔(秒)
  performance_tracking: true
  error_tracking: true
  usage_statistics: true

# 通知配置
notifications:
  enabled: false
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from_address: ""
    to_addresses: []
  webhook:
    enabled: false
    url: ""
    headers: {}

# 备份配置
backup:
  enabled: true
  backup_dir: "backups"
  auto_backup: true
  backup_interval: 86400  # 24小时
  max_backups: 7
  compress: true

# 更新配置
updates:
  auto_check: true
  check_interval: 86400  # 24小时
  auto_update: false
  update_channel: "stable"  # stable, beta, dev

# 实验性功能
experimental:
  enabled: false
  features:
    - "advanced_planning"
    - "multi_agent_collaboration"
    - "real_time_learning"
    - "voice_interface"

# 环境变量映射
environment:
  # 从环境变量读取敏感配置
  openai_api_key: "OPENAI_API_KEY"
  anthropic_api_key: "ANTHROPIC_API_KEY"
  database_url: "DATABASE_URL"
  redis_url: "REDIS_URL"
  log_level: "LOG_LEVEL"
  debug: "DEBUG"
