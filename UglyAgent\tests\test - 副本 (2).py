```python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n - 1):
        swapped = False
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
                swapped = True
        if not swapped:
            break

def test_bubble_sort():
    import random
    test_cases = [
        [random.randint(0, 100) for _ in range(10)],
        [5, 3, 8, 1],
        [],
        [1],
        [9, 7, 5, 3]
    ]
    
    for case in test_cases:
        expected = sorted(case)
        bubble_sort(case)
        assert case == expected, f"Failed: {case} != {expected}"
    print("All tests passed!")

if __name__ == "__main__":
    test_bubble_sort()
```

代码说明：
1. `bubble_sort` 函数实现了优化版冒泡排序，包含提前终止机制
2. `test_bubble_sort` 包含随机测试和边界测试用例，验证排序正确性
3. 测试用例包括空列表、单元素列表、逆序列表等特殊情况
4. 使用Python的断言机制进行自动验证
5. 直接运行脚本会执行完整的测试套件，输出测试结果