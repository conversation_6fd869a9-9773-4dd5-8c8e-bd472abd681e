#!/usr/bin/env python3
"""
UglyAgent 记忆系统
提供长期记忆、短期记忆、上下文管理等功能
"""

import json
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from pathlib import Path
import sqlite3
import threading

logger = logging.getLogger(__name__)


class MemoryType(Enum):
    """记忆类型"""
    SHORT_TERM = "short_term"  # 短期记忆（会话内）
    LONG_TERM = "long_term"    # 长期记忆（持久化）
    WORKING = "working"        # 工作记忆（当前任务）
    EPISODIC = "episodic"      # 情节记忆（事件序列）
    SEMANTIC = "semantic"      # 语义记忆（知识和概念）


@dataclass
class MemoryItem:
    """记忆项"""
    id: str
    content: str
    memory_type: MemoryType
    timestamp: float
    importance: float = 0.5  # 重要性评分 0-1
    access_count: int = 0
    last_accessed: float = 0.0
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if self.last_accessed == 0.0:
            self.last_accessed = self.timestamp


class MemoryManager:
    """记忆管理器"""
    
    def __init__(self, db_path: str = "memory.db", max_short_term: int = 1000):
        self.db_path = db_path
        self.max_short_term = max_short_term
        self.short_term_memory: Dict[str, MemoryItem] = {}
        self.working_memory: Dict[str, MemoryItem] = {}
        self.lock = threading.RLock()
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS memories (
                        id TEXT PRIMARY KEY,
                        content TEXT NOT NULL,
                        memory_type TEXT NOT NULL,
                        timestamp REAL NOT NULL,
                        importance REAL DEFAULT 0.5,
                        access_count INTEGER DEFAULT 0,
                        last_accessed REAL NOT NULL,
                        tags TEXT,
                        metadata TEXT
                    )
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_memory_type ON memories(memory_type)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_timestamp ON memories(timestamp)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_importance ON memories(importance)
                """)
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
    
    def _generate_memory_id(self, content: str, memory_type: MemoryType) -> str:
        """生成记忆ID"""
        data = f"{content}_{memory_type.value}_{time.time()}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def add_memory(self, content: str, memory_type: MemoryType,
                   importance: float = 0.5, tags: List[str] = None,
                   metadata: Dict[str, Any] = None) -> str:
        """添加记忆"""
        try:
            with self.lock:
                memory_id = self._generate_memory_id(content, memory_type)
                
                memory_item = MemoryItem(
                    id=memory_id,
                    content=content,
                    memory_type=memory_type,
                    timestamp=time.time(),
                    importance=importance,
                    tags=tags or [],
                    metadata=metadata or {}
                )
                
                if memory_type == MemoryType.SHORT_TERM:
                    self._add_short_term_memory(memory_item)
                elif memory_type == MemoryType.WORKING:
                    self.working_memory[memory_id] = memory_item
                else:
                    self._add_long_term_memory(memory_item)
                
                logger.debug(f"添加记忆: {memory_id} ({memory_type.value})")
                return memory_id
                
        except Exception as e:
            logger.error(f"添加记忆失败: {e}")
            return ""
    
    def _add_short_term_memory(self, memory_item: MemoryItem):
        """添加短期记忆"""
        # 检查容量限制
        if len(self.short_term_memory) >= self.max_short_term:
            # 移除最旧的记忆
            oldest_id = min(self.short_term_memory.keys(),
                           key=lambda x: self.short_term_memory[x].timestamp)
            del self.short_term_memory[oldest_id]
        
        self.short_term_memory[memory_item.id] = memory_item
    
    def _add_long_term_memory(self, memory_item: MemoryItem):
        """添加长期记忆"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO memories 
                    (id, content, memory_type, timestamp, importance, 
                     access_count, last_accessed, tags, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    memory_item.id,
                    memory_item.content,
                    memory_item.memory_type.value,
                    memory_item.timestamp,
                    memory_item.importance,
                    memory_item.access_count,
                    memory_item.last_accessed,
                    json.dumps(memory_item.tags),
                    json.dumps(memory_item.metadata)
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"添加长期记忆失败: {e}")
    
    def get_memory(self, memory_id: str) -> Optional[MemoryItem]:
        """获取记忆"""
        try:
            with self.lock:
                # 先检查短期记忆
                if memory_id in self.short_term_memory:
                    memory_item = self.short_term_memory[memory_id]
                    self._update_access_info(memory_item)
                    return memory_item
                
                # 检查工作记忆
                if memory_id in self.working_memory:
                    memory_item = self.working_memory[memory_id]
                    self._update_access_info(memory_item)
                    return memory_item
                
                # 检查长期记忆
                return self._get_long_term_memory(memory_id)
                
        except Exception as e:
            logger.error(f"获取记忆失败: {e}")
            return None
    
    def _get_long_term_memory(self, memory_id: str) -> Optional[MemoryItem]:
        """获取长期记忆"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT * FROM memories WHERE id = ?
                """, (memory_id,))
                
                row = cursor.fetchone()
                if row:
                    memory_item = self._row_to_memory_item(row)
                    self._update_access_info(memory_item)
                    
                    # 更新数据库中的访问信息
                    conn.execute("""
                        UPDATE memories 
                        SET access_count = ?, last_accessed = ?
                        WHERE id = ?
                    """, (memory_item.access_count, memory_item.last_accessed, memory_id))
                    conn.commit()
                    
                    return memory_item
                
                return None
                
        except Exception as e:
            logger.error(f"获取长期记忆失败: {e}")
            return None
    
    def _row_to_memory_item(self, row) -> MemoryItem:
        """数据库行转换为记忆项"""
        return MemoryItem(
            id=row[0],
            content=row[1],
            memory_type=MemoryType(row[2]),
            timestamp=row[3],
            importance=row[4],
            access_count=row[5],
            last_accessed=row[6],
            tags=json.loads(row[7]) if row[7] else [],
            metadata=json.loads(row[8]) if row[8] else {}
        )
    
    def _update_access_info(self, memory_item: MemoryItem):
        """更新访问信息"""
        memory_item.access_count += 1
        memory_item.last_accessed = time.time()
    
    def search_memories(self, query: str, memory_types: List[MemoryType] = None,
                       limit: int = 10, min_importance: float = 0.0) -> List[MemoryItem]:
        """搜索记忆"""
        try:
            results = []
            
            # 搜索短期记忆
            if not memory_types or MemoryType.SHORT_TERM in memory_types:
                for memory_item in self.short_term_memory.values():
                    if (query.lower() in memory_item.content.lower() and
                        memory_item.importance >= min_importance):
                        results.append(memory_item)
            
            # 搜索工作记忆
            if not memory_types or MemoryType.WORKING in memory_types:
                for memory_item in self.working_memory.values():
                    if (query.lower() in memory_item.content.lower() and
                        memory_item.importance >= min_importance):
                        results.append(memory_item)
            
            # 搜索长期记忆
            long_term_types = [MemoryType.LONG_TERM, MemoryType.EPISODIC, MemoryType.SEMANTIC]
            if not memory_types or any(t in memory_types for t in long_term_types):
                results.extend(self._search_long_term_memories(query, memory_types, min_importance))
            
            # 按重要性和最近访问时间排序
            results.sort(key=lambda x: (x.importance, x.last_accessed), reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            return []
    
    def _search_long_term_memories(self, query: str, memory_types: List[MemoryType] = None,
                                  min_importance: float = 0.0) -> List[MemoryItem]:
        """搜索长期记忆"""
        try:
            results = []
            
            with sqlite3.connect(self.db_path) as conn:
                sql = """
                    SELECT * FROM memories 
                    WHERE content LIKE ? AND importance >= ?
                """
                params = [f"%{query}%", min_importance]
                
                if memory_types:
                    type_placeholders = ",".join("?" * len(memory_types))
                    sql += f" AND memory_type IN ({type_placeholders})"
                    params.extend([t.value for t in memory_types])
                
                sql += " ORDER BY importance DESC, last_accessed DESC"
                
                cursor = conn.execute(sql, params)
                
                for row in cursor.fetchall():
                    results.append(self._row_to_memory_item(row))
            
            return results
            
        except Exception as e:
            logger.error(f"搜索长期记忆失败: {e}")
            return []
    
    def get_recent_memories(self, memory_type: MemoryType = None,
                           limit: int = 10, hours: int = 24) -> List[MemoryItem]:
        """获取最近的记忆"""
        try:
            cutoff_time = time.time() - (hours * 3600)
            results = []
            
            # 搜索短期记忆
            if not memory_type or memory_type == MemoryType.SHORT_TERM:
                for memory_item in self.short_term_memory.values():
                    if memory_item.timestamp >= cutoff_time:
                        results.append(memory_item)
            
            # 搜索工作记忆
            if not memory_type or memory_type == MemoryType.WORKING:
                for memory_item in self.working_memory.values():
                    if memory_item.timestamp >= cutoff_time:
                        results.append(memory_item)
            
            # 搜索长期记忆
            if not memory_type or memory_type in [MemoryType.LONG_TERM, MemoryType.EPISODIC, MemoryType.SEMANTIC]:
                with sqlite3.connect(self.db_path) as conn:
                    sql = "SELECT * FROM memories WHERE timestamp >= ?"
                    params = [cutoff_time]
                    
                    if memory_type:
                        sql += " AND memory_type = ?"
                        params.append(memory_type.value)
                    
                    sql += " ORDER BY timestamp DESC"
                    
                    cursor = conn.execute(sql, params)
                    
                    for row in cursor.fetchall():
                        results.append(self._row_to_memory_item(row))
            
            # 按时间排序
            results.sort(key=lambda x: x.timestamp, reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"获取最近记忆失败: {e}")
            return []
    
    def clear_working_memory(self):
        """清空工作记忆"""
        with self.lock:
            self.working_memory.clear()
            logger.info("工作记忆已清空")
    
    def consolidate_memories(self, importance_threshold: float = 0.7):
        """整合记忆（将重要的短期记忆转为长期记忆）"""
        try:
            with self.lock:
                consolidated_count = 0
                
                for memory_id, memory_item in list(self.short_term_memory.items()):
                    if memory_item.importance >= importance_threshold:
                        # 转为长期记忆
                        memory_item.memory_type = MemoryType.LONG_TERM
                        self._add_long_term_memory(memory_item)
                        
                        # 从短期记忆中移除
                        del self.short_term_memory[memory_id]
                        consolidated_count += 1
                
                logger.info(f"整合了 {consolidated_count} 条记忆")
                return consolidated_count
                
        except Exception as e:
            logger.error(f"记忆整合失败: {e}")
            return 0
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        try:
            stats = {
                "short_term_count": len(self.short_term_memory),
                "working_memory_count": len(self.working_memory),
                "long_term_count": 0,
                "total_count": 0,
                "memory_types": {}
            }
            
            # 统计长期记忆
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM memories")
                stats["long_term_count"] = cursor.fetchone()[0]
                
                # 按类型统计
                cursor = conn.execute("""
                    SELECT memory_type, COUNT(*) 
                    FROM memories 
                    GROUP BY memory_type
                """)
                
                for row in cursor.fetchall():
                    stats["memory_types"][row[0]] = row[1]
            
            stats["total_count"] = (stats["short_term_count"] + 
                                  stats["working_memory_count"] + 
                                  stats["long_term_count"])
            
            return stats
            
        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            return {}
    
    def cleanup_old_memories(self, days: int = 30, min_importance: float = 0.3):
        """清理旧记忆"""
        try:
            cutoff_time = time.time() - (days * 24 * 3600)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM memories 
                    WHERE timestamp < ? AND importance < ?
                """, (cutoff_time, min_importance))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧记忆")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理旧记忆失败: {e}")
            return 0
