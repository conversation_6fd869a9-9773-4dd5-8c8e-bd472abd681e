#!/usr/bin/env python3
"""
UglyAgent MCP客户端示例
演示如何作为MCP客户端连接到UglyAgent服务器
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from UglyAgent.mcp.protocol import MCPProtocolHandler, MCPRequest, MCPResponse


class MCPClient:
    """简单的MCP客户端"""
    
    def __init__(self):
        self.protocol = MCPProtocolHandler()
        self.initialized = False
        self.server_capabilities = {}
        self.available_tools = []
    
    async def connect_stdio(self, server_command: list):
        """通过stdio连接到MCP服务器"""
        print(f"🔌 连接到MCP服务器: {' '.join(server_command)}")
        
        # 启动服务器进程
        self.process = await asyncio.create_subprocess_exec(
            *server_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        print("✅ 服务器进程已启动")
        return True
    
    async def send_request(self, method: str, params: dict = None) -> dict:
        """发送MCP请求"""
        request = MCPRequest(
            id=f"req_{asyncio.get_event_loop().time()}",
            method=method,
            params=params
        )
        
        # 序列化请求
        request_json = json.dumps({
            "jsonrpc": request.jsonrpc,
            "id": request.id,
            "method": request.method,
            "params": request.params
        })
        
        print(f"📤 发送请求: {method}")
        
        # 发送到服务器
        self.process.stdin.write((request_json + '\n').encode())
        await self.process.stdin.drain()
        
        # 读取响应
        response_line = await self.process.stdout.readline()
        response_data = json.loads(response_line.decode().strip())
        
        print(f"📥 收到响应: {response_data.get('result', {}).get('status', 'success')}")
        
        return response_data
    
    async def initialize(self):
        """初始化MCP连接"""
        print("🚀 初始化MCP连接...")
        
        # 发送初始化请求
        response = await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {},
                "resources": {},
                "prompts": {}
            },
            "clientInfo": {
                "name": "UglyAgent MCP Client Example",
                "version": "1.0.0"
            }
        })
        
        if "result" in response:
            self.server_capabilities = response["result"]["capabilities"]
            print(f"✅ 服务器能力: {list(self.server_capabilities.keys())}")
            
            # 发送初始化完成通知
            await self.send_notification("initialized", {})
            self.initialized = True
            
            return True
        else:
            print(f"❌ 初始化失败: {response.get('error', 'Unknown error')}")
            return False
    
    async def send_notification(self, method: str, params: dict = None):
        """发送MCP通知"""
        notification = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params or {}
        }
        
        notification_json = json.dumps(notification)
        self.process.stdin.write((notification_json + '\n').encode())
        await self.process.stdin.drain()
    
    async def list_tools(self):
        """列出可用工具"""
        print("🔧 获取可用工具...")
        
        response = await self.send_request("tools/list", {})
        
        if "result" in response:
            self.available_tools = response["result"]["tools"]
            print(f"✅ 发现 {len(self.available_tools)} 个工具:")
            
            for tool in self.available_tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            return self.available_tools
        else:
            print(f"❌ 获取工具失败: {response.get('error', 'Unknown error')}")
            return []
    
    async def call_tool(self, tool_name: str, arguments: dict = None):
        """调用工具"""
        print(f"⚡ 调用工具: {tool_name}")
        
        response = await self.send_request("tools/call", {
            "name": tool_name,
            "arguments": arguments or {}
        })
        
        if "result" in response:
            result = response["result"]
            print(f"✅ 工具调用成功")
            
            # 显示结果
            if "content" in result:
                for content in result["content"]:
                    if content["type"] == "text":
                        print(f"📄 结果: {content['text'][:200]}...")
            
            return result
        else:
            print(f"❌ 工具调用失败: {response.get('error', 'Unknown error')}")
            return None
    
    async def list_resources(self):
        """列出可用资源"""
        print("📚 获取可用资源...")
        
        response = await self.send_request("resources/list", {})
        
        if "result" in response:
            resources = response["result"]["resources"]
            print(f"✅ 发现 {len(resources)} 个资源:")
            
            for resource in resources:
                print(f"  - {resource['uri']}: {resource['name']}")
            
            return resources
        else:
            print(f"❌ 获取资源失败: {response.get('error', 'Unknown error')}")
            return []
    
    async def read_resource(self, uri: str):
        """读取资源"""
        print(f"📖 读取资源: {uri}")
        
        response = await self.send_request("resources/read", {
            "uri": uri
        })
        
        if "result" in response:
            contents = response["result"]["contents"]
            print(f"✅ 资源读取成功")
            
            for content in contents:
                print(f"📄 内容: {content['text'][:200]}...")
            
            return contents
        else:
            print(f"❌ 资源读取失败: {response.get('error', 'Unknown error')}")
            return None
    
    async def close(self):
        """关闭连接"""
        print("🔌 关闭MCP连接...")
        
        if hasattr(self, 'process'):
            self.process.terminate()
            await self.process.wait()
        
        print("✅ 连接已关闭")


async def file_operations_demo(client: MCPClient):
    """文件操作演示"""
    print("\n📁 文件操作演示")
    print("=" * 50)
    
    # 创建测试文件
    await client.call_tool("write_file", {
        "file_path": "mcp_test.txt",
        "content": "这是通过MCP创建的测试文件\n包含多行内容\n用于演示文件操作功能"
    })
    
    # 读取文件
    await client.call_tool("read_file", {
        "file_path": "mcp_test.txt"
    })
    
    # 列出目录
    await client.call_tool("list_directory", {
        "directory_path": "."
    })


async def code_analysis_demo(client: MCPClient):
    """代码分析演示"""
    print("\n🔍 代码分析演示")
    print("=" * 50)
    
    # 创建示例Python文件
    python_code = '''def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def main():
    for i in range(10):
        print(f"fibonacci({i}) = {fibonacci(i)}")

if __name__ == "__main__":
    main()
'''
    
    await client.call_tool("write_file", {
        "file_path": "fibonacci.py",
        "content": python_code
    })
    
    # 分析代码
    await client.call_tool("analyze_code", {
        "file_path": "fibonacci.py"
    })
    
    # 检查语法
    await client.call_tool("check_syntax", {
        "file_path": "fibonacci.py"
    })


async def web_tools_demo(client: MCPClient):
    """网络工具演示"""
    print("\n🌐 网络工具演示")
    print("=" * 50)
    
    # HTTP请求
    await client.call_tool("http_request", {
        "url": "https://httpbin.org/json",
        "method": "GET"
    })
    
    # 检查URL状态
    await client.call_tool("check_url_status", {
        "urls": ["https://httpbin.org/status/200", "https://httpbin.org/status/404"]
    })


async def system_tools_demo(client: MCPClient):
    """系统工具演示"""
    print("\n💻 系统工具演示")
    print("=" * 50)
    
    # 获取系统信息
    await client.call_tool("get_system_info", {
        "info_type": "basic"
    })
    
    # 检查磁盘空间
    await client.call_tool("check_disk_space", {
        "paths": ["."]
    })


async def ai_chat_demo(client: MCPClient):
    """AI对话演示"""
    print("\n🤖 AI对话演示")
    print("=" * 50)
    
    # AI对话
    await client.call_tool("ai_chat", {
        "message": "请简单介绍一下UglyAgent的主要功能",
        "system_prompt": "你是UglyAgent，一个强大的AI助手。"
    })


async def resources_demo(client: MCPClient):
    """资源演示"""
    print("\n📚 资源演示")
    print("=" * 50)
    
    # 列出资源
    resources = await client.list_resources()
    
    # 读取配置资源
    if resources:
        for resource in resources:
            if "config" in resource["uri"]:
                await client.read_resource(resource["uri"])
                break


async def main():
    """主函数"""
    print("🤖 UglyAgent MCP客户端示例")
    print("=" * 80)
    
    # 创建客户端
    client = MCPClient()
    
    try:
        # 连接到服务器
        server_command = [
            sys.executable, "main.py", "mcp-stdio"
        ]
        
        await client.connect_stdio(server_command)
        
        # 初始化连接
        if await client.initialize():
            print("🎉 MCP连接初始化成功！")
            
            # 列出可用工具
            await client.list_tools()
            
            # 运行演示
            demos = [
                file_operations_demo,
                code_analysis_demo,
                web_tools_demo,
                system_tools_demo,
                ai_chat_demo,
                resources_demo
            ]
            
            for i, demo in enumerate(demos, 1):
                try:
                    print(f"\n[{i}/{len(demos)}] 运行演示: {demo.__name__}")
                    await demo(client)
                    print("✅ 演示完成")
                except Exception as e:
                    print(f"❌ 演示失败: {e}")
                
                # 添加分隔符
                if i < len(demos):
                    print("\n" + "-" * 50)
            
            print("\n🎉 所有演示完成！")
        
        else:
            print("❌ MCP连接初始化失败")
    
    except Exception as e:
        print(f"❌ 客户端运行失败: {e}")
    
    finally:
        # 关闭连接
        await client.close()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 运行失败: {e}")
        print("\n💡 提示:")
        print("- 确保UglyAgent已正确安装")
        print("- 确保配置文件存在且正确")
        print("- 检查Python路径和依赖")
