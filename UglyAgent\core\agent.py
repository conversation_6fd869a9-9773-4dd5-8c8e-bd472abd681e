#!/usr/bin/env python3
"""
UglyAgent 核心Agent类
整合所有组件，提供统一的AI Agent接口
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

from llm_manager import LLMManager, ModelResponse
from memory import MemoryManager, MemoryType
from planner import TaskPlanner, TaskPriority
from tools.file_ops import FileOperationTools
from code_analysis import CodeAnalysisTools
from web_tools import WebTools
from system_tools import SystemTools
from settings import get_config, get_agent_settings

# from ..tools.file_ops import FileOperationTools
# from ..tools.code_analysis import CodeAnalysisTools
# from ..tools.web_tools import WebTools
# from ..tools.system_tools import SystemTools
# from ..config.settings import get_config, get_agent_settings


logger = logging.getLogger(__name__)


class UglyAgent:
    """UglyAgent 主类 - 超越Augment的AI Agent"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = get_config()
        self.agent_settings = get_agent_settings()
        
        # 核心组件
        self.llm_manager: Optional[LLMManager] = None
        self.memory_manager = MemoryManager()
        self.task_planner = TaskPlanner()
        
        # 工具集
        self.file_tools = FileOperationTools()
        self.code_tools = CodeAnalysisTools()
        self.web_tools = WebTools()
        self.system_tools = SystemTools()
        
        # 会话状态
        self.session_id = None
        self.conversation_history: List[Dict[str, Any]] = []
        self.current_context: Dict[str, Any] = {}
        
        # 初始化
        self._setup_components()
    
    def _setup_components(self):
        """设置组件"""
        try:
            # 注册任务执行器
            self._register_task_executors()
            
            logger.info("UglyAgent 初始化完成")
            
        except Exception as e:
            logger.error(f"UglyAgent 初始化失败: {e}")
    
    def _register_task_executors(self):
        """注册任务执行器"""
        # 文件操作工具
        self.task_planner.register_executor("read_file", self.file_tools.read_file)
        self.task_planner.register_executor("write_file", self.file_tools.write_file)
        self.task_planner.register_executor("list_directory", self.file_tools.list_directory)
        self.task_planner.register_executor("search_files", self.file_tools.search_files)
        self.task_planner.register_executor("copy_file", self.file_tools.copy_file)
        self.task_planner.register_executor("move_file", self.file_tools.move_file)
        self.task_planner.register_executor("delete_file", self.file_tools.delete_file)
        self.task_planner.register_executor("get_file_hash", self.file_tools.get_file_hash)
        
        # 代码分析工具
        self.task_planner.register_executor("analyze_code", self.code_tools.analyze_code)
        self.task_planner.register_executor("extract_functions", self.code_tools.extract_functions)
        self.task_planner.register_executor("check_syntax", self.code_tools.check_syntax)
        
        # Web工具
        self.task_planner.register_executor("http_request", self._web_http_request)
        self.task_planner.register_executor("scrape_webpage", self._web_scrape_webpage)
        self.task_planner.register_executor("check_url_status", self._web_check_url_status)
        
        # 系统工具
        self.task_planner.register_executor("execute_command", self.system_tools.execute_command)
        self.task_planner.register_executor("get_system_info", self.system_tools.get_system_info)
        self.task_planner.register_executor("monitor_system", self.system_tools.monitor_system)
        self.task_planner.register_executor("check_disk_space", self.system_tools.check_disk_space)
        
        # AI对话工具
        self.task_planner.register_executor("ai_chat", self._ai_chat_executor)
    
    async def _web_http_request(self, **kwargs):
        """Web HTTP请求执行器"""
        async with self.web_tools as web:
            return await web.http_request(**kwargs)
    
    async def _web_scrape_webpage(self, **kwargs):
        """网页抓取执行器"""
        async with self.web_tools as web:
            return await web.scrape_webpage(**kwargs)
    
    async def _web_check_url_status(self, **kwargs):
        """URL状态检查执行器"""
        async with self.web_tools as web:
            return await web.check_url_status(**kwargs)
    
    async def _ai_chat_executor(self, **kwargs):
        """AI对话执行器"""
        if not self.llm_manager:
            self.llm_manager = LLMManager()
            await self.llm_manager.__aenter__()

        # 这个方法会被任务规划器的详细显示逻辑调用
        # 详细的交互过程在 _execute_ai_chat_with_details 中处理
        response = await self.llm_manager.chat(**kwargs)
        return {
            "success": response.success,
            "content": response.content,
            "model": response.model,
            "tokens_used": response.tokens_used,
            "cost": response.cost,
            "response_time": response.response_time,
            "error": response.error
        }
    
    async def start_session(self, session_id: Optional[str] = None) -> str:
        """启动会话"""
        if not session_id:
            session_id = f"session_{int(time.time())}"
        
        self.session_id = session_id
        self.conversation_history = []
        self.current_context = {}
        
        # 初始化LLM管理器
        if not self.llm_manager:
            self.llm_manager = LLMManager()
            await self.llm_manager.__aenter__()
        
        # 添加会话开始记忆
        self.memory_manager.add_memory(
            content=f"会话开始: {session_id}",
            memory_type=MemoryType.EPISODIC,
            importance=0.3,
            tags=["session", "start"],
            metadata={"session_id": session_id}
        )
        
        logger.info(f"会话已启动: {session_id}")
        return session_id
    
    async def chat(self, message: str, model: Optional[str] = None,
                   system_prompt: Optional[str] = None,
                   use_memory: bool = True,
                   use_planning: bool = False) -> Dict[str, Any]:
        """与AI对话"""
        try:
            if not self.session_id:
                await self.start_session()
            
            start_time = time.time()
            
            # 添加用户消息到记忆
            if use_memory:
                self.memory_manager.add_memory(
                    content=f"用户: {message}",
                    memory_type=MemoryType.SHORT_TERM,
                    importance=0.6,
                    tags=["user", "message"],
                    metadata={"session_id": self.session_id}
                )
            
            # 构建上下文
            context = await self._build_context(message, use_memory)
            
            # 构建系统提示
            if not system_prompt:
                system_prompt = self._build_system_prompt(context)
            
            # 判断是否需要使用规划
            if use_planning or self._should_use_planning(message):
                return await self._handle_with_planning(message, context)
            else:
                return await self._handle_simple_chat(message, model, system_prompt, context)
                
        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，处理您的请求时出现了错误。"
            }
    
    async def _build_context(self, message: str, use_memory: bool) -> Dict[str, Any]:
        """构建对话上下文"""
        context = {
            "current_message": message,
            "session_id": self.session_id,
            "timestamp": time.time()
        }
        
        if use_memory:
            # 搜索相关记忆
            relevant_memories = self.memory_manager.search_memories(
                query=message,
                limit=5,
                min_importance=0.4
            )
            
            context["relevant_memories"] = [
                {
                    "content": mem.content,
                    "importance": mem.importance,
                    "timestamp": mem.timestamp
                }
                for mem in relevant_memories
            ]
            
            # 获取最近的对话历史
            recent_memories = self.memory_manager.get_recent_memories(
                memory_type=MemoryType.SHORT_TERM,
                limit=10,
                hours=1
            )
            
            context["recent_conversation"] = [
                mem.content for mem in recent_memories
            ]
        
        return context
    
    def _build_system_prompt(self, context: Dict[str, Any]) -> str:
        """构建系统提示"""
        base_prompt = f"""你是UglyAgent，一个超越Augment的强大AI助手。

你的能力包括：
- 文件操作：读取、写入、搜索、分析文件
- 代码分析：分析代码结构、检查语法、提取函数
- 网络工具：HTTP请求、网页抓取、URL检查
- 系统工具：执行命令、监控系统、获取系统信息
- 任务规划：分解复杂任务并自动执行
- 记忆管理：记住重要信息和对话历史

当前会话ID: {context.get('session_id', 'unknown')}
"""
        
        # 添加相关记忆
        if context.get("relevant_memories"):
            base_prompt += "\n相关记忆：\n"
            for mem in context["relevant_memories"]:
                base_prompt += f"- {mem['content']}\n"
        
        # 添加最近对话
        if context.get("recent_conversation"):
            base_prompt += "\n最近对话：\n"
            for msg in context["recent_conversation"][-5:]:  # 只显示最近5条
                base_prompt += f"- {msg}\n"
        
        base_prompt += """
请根据用户的请求提供帮助。如果需要执行复杂任务，我会自动创建执行计划。
如果需要使用工具，我会调用相应的工具来完成任务。
"""
        
        return base_prompt
    
    def _should_use_planning(self, message: str) -> bool:
        """判断是否需要使用任务规划"""
        planning_keywords = [
            "分析文件", "analyze file", "检查代码", "code review",
            "爬取网页", "scrape", "监控系统", "monitor system",
            "批量处理", "batch process", "自动化", "automate"
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in planning_keywords)
    
    async def _handle_simple_chat(self, message: str, model: Optional[str],
                                 system_prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理简单对话"""
        response = await self.llm_manager.chat(
            message=message,
            model=model,
            system_prompt=system_prompt
        )
        
        # 添加AI响应到记忆
        self.memory_manager.add_memory(
            content=f"AI: {response.content}",
            memory_type=MemoryType.SHORT_TERM,
            importance=0.5,
            tags=["ai", "response"],
            metadata={"session_id": self.session_id, "model": response.model}
        )
        
        return {
            "success": response.success,
            "response": response.content,
            "model": response.model,
            "tokens_used": response.tokens_used,
            "cost": response.cost,
            "response_time": response.response_time,
            "error": response.error,
            "used_planning": False
        }
    
    async def _handle_with_planning(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """使用任务规划处理复杂请求"""
        try:
            print(f"\n" + "="*60)
            print(f"🎯 UglyAgent 开始处理复杂任务")
            print(f"="*60)

            logger.info(f"开始任务规划处理: {message}")

            # 创建执行计划
            plan_id = self.task_planner.create_plan(message, context)

            if not plan_id:
                return {
                    "success": False,
                    "error": "创建执行计划失败",
                    "response": "抱歉，无法为您的请求创建执行计划。"
                }

            print(f"\n" + "-"*50)
            print(f"🚀 开始执行计划")
            print(f"-"*50)

            # 执行计划
            execution_result = await self.task_planner.execute_plan(plan_id)

            print(f"\n" + "-"*50)
            print(f"📋 执行总结")
            print(f"-"*50)

            logger.info(f"计划执行完成: {execution_result}")

            # 获取计划状态
            plan_status = self.task_planner.get_plan_status(plan_id)

            # 构建响应
            if execution_result["success"]:
                response_text = f"已完成您的请求！\n\n"
                response_text += f"执行了 {execution_result['completed_tasks']} 个任务，"
                response_text += f"耗时 {execution_result['execution_time']:.2f} 秒。\n\n"

                # 添加任务结果摘要
                plan = self.task_planner.plans[plan_id]
                for task in plan.tasks:
                    if task.result:
                        logger.info(f"任务 {task.name} 结果: {task.result}")
                        if isinstance(task.result, dict) and task.result.get("success"):
                            response_text += f"✓ {task.name}: 完成\n"
                            if "file_path" in task.result:
                                response_text += f"  文件已保存到: {task.result['file_path']}\n"
                        else:
                            response_text += f"✗ {task.name}: 执行失败\n"
                    elif task.error:
                        response_text += f"✗ {task.name}: {task.error}\n"

                print(f"🎉 任务执行成功!")
                print(f"⏱️ 总耗时: {execution_result['execution_time']:.2f} 秒")
                print(f"✅ 完成任务: {execution_result['completed_tasks']}/{len(plan.tasks)}")

            else:
                response_text = f"执行过程中遇到问题：{execution_result.get('error', '未知错误')}"
                print(f"❌ 任务执行失败: {execution_result.get('error', '未知错误')}")

            print(f"="*60)

            # 添加到记忆
            self.memory_manager.add_memory(
                content=f"执行计划: {message} -> {response_text}",
                memory_type=MemoryType.EPISODIC,
                importance=0.8,
                tags=["planning", "execution"],
                metadata={
                    "session_id": self.session_id,
                    "plan_id": plan_id,
                    "execution_result": execution_result
                }
            )

            return {
                "success": execution_result["success"],
                "response": response_text,
                "plan_id": plan_id,
                "execution_result": execution_result,
                "plan_status": plan_status,
                "used_planning": True
            }

        except Exception as e:
            print(f"❌ 任务规划处理失败: {e}")
            logger.error(f"任务规划处理失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，执行您的请求时出现了错误。",
                "used_planning": True
            }
    
    async def execute_task(self, goal: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行复杂任务"""
        return await self._handle_with_planning(goal, context or {})
    
    def get_conversation_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取对话历史"""
        if not self.session_id:
            return []
        
        memories = self.memory_manager.get_recent_memories(
            memory_type=MemoryType.SHORT_TERM,
            limit=limit,
            hours=24
        )
        
        return [
            {
                "content": mem.content,
                "timestamp": mem.timestamp,
                "importance": mem.importance
            }
            for mem in memories
        ]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        return {
            "session_id": self.session_id,
            "session_active": self.session_id is not None,
            "memory_stats": self.memory_manager.get_memory_stats(),
            "llm_metrics": self.llm_manager.get_metrics() if self.llm_manager else {},
            "available_models": self.llm_manager.get_available_models() if self.llm_manager else [],
            "conversation_length": len(self.conversation_history),
            "agent_info": {
                "name": self.agent_settings.name,
                "version": self.agent_settings.version,
                "description": self.agent_settings.description
            }
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.llm_manager:
                await self.llm_manager.__aexit__(None, None, None)
            
            # 整合重要记忆
            self.memory_manager.consolidate_memories()
            
            logger.info("UglyAgent 资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
