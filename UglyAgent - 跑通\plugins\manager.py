#!/usr/bin/env python3
"""
UglyAgent 插件管理器
提供插件的生命周期管理、事件系统、权限控制等功能
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from pathlib import Path

from .base import BasePlugin, PluginLoader, PluginStatus, PluginInfo

logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型"""
    PLUGIN_LOADED = "plugin_loaded"
    PLUGIN_UNLOADED = "plugin_unloaded"
    PLUGIN_ACTIVATED = "plugin_activated"
    PLUGIN_DEACTIVATED = "plugin_deactivated"
    PLUGIN_ERROR = "plugin_error"
    TOOL_CALLED = "tool_called"
    COMMAND_EXECUTED = "command_executed"


@dataclass
class Event:
    """事件类"""
    type: EventType
    plugin_name: str
    data: Dict[str, Any]
    timestamp: float
    
    def __post_init__(self):
        if not hasattr(self, 'timestamp') or self.timestamp == 0:
            self.timestamp = time.time()


class PluginManager:
    """插件管理器"""
    
    def __init__(self, plugin_dirs: List[str] = None, config_file: str = "plugins/config.json"):
        self.plugin_loader = PluginLoader(plugin_dirs)
        self.config_file = config_file
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}
        
        # 事件系统
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        self.event_history: List[Event] = []
        self.max_event_history = 1000
        
        # 权限系统
        self.permissions: Dict[str, Set[str]] = {}
        self.security_enabled = True
        
        # 工具和命令注册表
        self.registered_tools: Dict[str, tuple] = {}  # name -> (plugin_name, func)
        self.registered_commands: Dict[str, tuple] = {}  # name -> (plugin_name, func)
        self.registered_hooks: Dict[str, List[tuple]] = {}  # event -> [(plugin_name, func)]
        
        # 加载配置
        self._load_config()
        
        # 注册内置事件处理器
        self._register_builtin_handlers()
    
    def _load_config(self):
        """加载插件配置"""
        try:
            config_path = Path(self.config_file)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.plugin_configs = config.get("plugins", {})
                self.security_enabled = config.get("security_enabled", True)
                
                logger.info(f"已加载插件配置: {len(self.plugin_configs)} 个插件")
            
        except Exception as e:
            logger.error(f"加载插件配置失败: {e}")
    
    def _save_config(self):
        """保存插件配置"""
        try:
            config = {
                "plugins": self.plugin_configs,
                "security_enabled": self.security_enabled
            }
            
            config_path = Path(self.config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info("插件配置已保存")
            
        except Exception as e:
            logger.error(f"保存插件配置失败: {e}")
    
    def _register_builtin_handlers(self):
        """注册内置事件处理器"""
        self.register_event_handler(EventType.PLUGIN_ACTIVATED, self._on_plugin_activated)
        self.register_event_handler(EventType.PLUGIN_DEACTIVATED, self._on_plugin_deactivated)
        self.register_event_handler(EventType.PLUGIN_ERROR, self._on_plugin_error)
    
    async def _on_plugin_activated(self, event: Event):
        """插件激活事件处理"""
        plugin_name = event.plugin_name
        plugin = self.plugin_loader.get_plugin(plugin_name)
        
        if plugin:
            # 注册插件提供的工具和命令
            await self._register_plugin_features(plugin)
    
    async def _on_plugin_deactivated(self, event: Event):
        """插件停用事件处理"""
        plugin_name = event.plugin_name
        
        # 注销插件提供的工具和命令
        await self._unregister_plugin_features(plugin_name)
    
    async def _on_plugin_error(self, event: Event):
        """插件错误事件处理"""
        plugin_name = event.plugin_name
        error_info = event.data
        
        logger.error(f"插件错误 {plugin_name}: {error_info}")
        
        # 可以在这里实现错误恢复逻辑
    
    async def _register_plugin_features(self, plugin: BasePlugin):
        """注册插件功能"""
        plugin_name = plugin.info.name
        
        # 注册工具
        for tool_name, tool_func in plugin.get_tools().items():
            full_tool_name = f"{plugin_name}.{tool_name}"
            self.registered_tools[full_tool_name] = (plugin_name, tool_func)
            logger.info(f"注册工具: {full_tool_name}")
        
        # 注册命令
        for cmd_name, cmd_func in plugin.get_commands().items():
            full_cmd_name = f"{plugin_name}.{cmd_name}"
            self.registered_commands[full_cmd_name] = (plugin_name, cmd_func)
            logger.info(f"注册命令: {full_cmd_name}")
        
        # 注册钩子
        for event_name, hook_funcs in plugin.get_hooks().items():
            if event_name not in self.registered_hooks:
                self.registered_hooks[event_name] = []
            
            for hook_func in hook_funcs:
                self.registered_hooks[event_name].append((plugin_name, hook_func))
                logger.info(f"注册钩子: {plugin_name}.{event_name}")
    
    async def _unregister_plugin_features(self, plugin_name: str):
        """注销插件功能"""
        # 注销工具
        tools_to_remove = [
            name for name, (pname, _) in self.registered_tools.items()
            if pname == plugin_name
        ]
        for tool_name in tools_to_remove:
            del self.registered_tools[tool_name]
            logger.info(f"注销工具: {tool_name}")
        
        # 注销命令
        commands_to_remove = [
            name for name, (pname, _) in self.registered_commands.items()
            if pname == plugin_name
        ]
        for cmd_name in commands_to_remove:
            del self.registered_commands[cmd_name]
            logger.info(f"注销命令: {cmd_name}")
        
        # 注销钩子
        for event_name, hooks in self.registered_hooks.items():
            self.registered_hooks[event_name] = [
                (pname, func) for pname, func in hooks
                if pname != plugin_name
            ]
    
    def register_event_handler(self, event_type: EventType, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info(f"注册事件处理器: {event_type.value}")
    
    async def emit_event(self, event_type: EventType, plugin_name: str, data: Dict[str, Any] = None):
        """发出事件"""
        event = Event(
            type=event_type,
            plugin_name=plugin_name,
            data=data or {}
        )
        
        # 添加到历史记录
        self.event_history.append(event)
        if len(self.event_history) > self.max_event_history:
            self.event_history.pop(0)
        
        # 调用事件处理器
        handlers = self.event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                await handler(event)
            except Exception as e:
                logger.error(f"事件处理器执行失败: {e}")
    
    async def discover_and_load_plugins(self):
        """发现并加载插件"""
        try:
            # 发现插件
            discovered = self.plugin_loader.discover_plugins()
            logger.info(f"发现 {len(discovered)} 个插件")
            
            # 加载配置中启用的插件
            loaded_count = 0
            for plugin_name in discovered:
                plugin_config = self.plugin_configs.get(plugin_name, {})
                
                if plugin_config.get("enabled", False):
                    if await self.plugin_loader.load_plugin(plugin_name):
                        await self.emit_event(EventType.PLUGIN_LOADED, plugin_name)
                        loaded_count += 1
                        
                        # 如果配置为自动激活，则激活插件
                        if plugin_config.get("auto_activate", False):
                            await self.activate_plugin(plugin_name, plugin_config.get("config", {}))
            
            logger.info(f"加载了 {loaded_count} 个插件")
            return loaded_count
            
        except Exception as e:
            logger.error(f"发现和加载插件失败: {e}")
            return 0
    
    async def activate_plugin(self, plugin_name: str, config: Dict[str, Any] = None) -> bool:
        """激活插件"""
        try:
            # 检查权限
            if self.security_enabled and not self._check_plugin_permissions(plugin_name):
                logger.error(f"插件权限检查失败: {plugin_name}")
                return False
            
            # 激活插件
            if await self.plugin_loader.activate_plugin(plugin_name, config):
                await self.emit_event(EventType.PLUGIN_ACTIVATED, plugin_name, {"config": config})
                
                # 更新配置
                if plugin_name not in self.plugin_configs:
                    self.plugin_configs[plugin_name] = {}
                self.plugin_configs[plugin_name]["enabled"] = True
                self.plugin_configs[plugin_name]["auto_activate"] = True
                if config:
                    self.plugin_configs[plugin_name]["config"] = config
                
                self._save_config()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"激活插件失败 {plugin_name}: {e}")
            await self.emit_event(EventType.PLUGIN_ERROR, plugin_name, {"error": str(e)})
            return False
    
    async def deactivate_plugin(self, plugin_name: str) -> bool:
        """停用插件"""
        try:
            if await self.plugin_loader.deactivate_plugin(plugin_name):
                await self.emit_event(EventType.PLUGIN_DEACTIVATED, plugin_name)
                
                # 更新配置
                if plugin_name in self.plugin_configs:
                    self.plugin_configs[plugin_name]["auto_activate"] = False
                
                self._save_config()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"停用插件失败 {plugin_name}: {e}")
            await self.emit_event(EventType.PLUGIN_ERROR, plugin_name, {"error": str(e)})
            return False
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件"""
        try:
            if await self.plugin_loader.unload_plugin(plugin_name):
                await self.emit_event(EventType.PLUGIN_UNLOADED, plugin_name)
                
                # 更新配置
                if plugin_name in self.plugin_configs:
                    self.plugin_configs[plugin_name]["enabled"] = False
                
                self._save_config()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"卸载插件失败 {plugin_name}: {e}")
            await self.emit_event(EventType.PLUGIN_ERROR, plugin_name, {"error": str(e)})
            return False
    
    def _check_plugin_permissions(self, plugin_name: str) -> bool:
        """检查插件权限"""
        if not self.security_enabled:
            return True
        
        # 这里可以实现更复杂的权限检查逻辑
        # 目前简单返回True
        return True
    
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """调用工具"""
        try:
            if tool_name not in self.registered_tools:
                raise ValueError(f"未找到工具: {tool_name}")
            
            plugin_name, tool_func = self.registered_tools[tool_name]
            
            # 检查插件是否激活
            plugin = self.plugin_loader.get_plugin(plugin_name)
            if not plugin or not plugin.is_active():
                raise ValueError(f"插件未激活: {plugin_name}")
            
            # 调用工具
            result = await tool_func(**kwargs)
            
            # 发出事件
            await self.emit_event(EventType.TOOL_CALLED, plugin_name, {
                "tool_name": tool_name,
                "kwargs": kwargs,
                "result": result
            })
            
            return result
            
        except Exception as e:
            logger.error(f"调用工具失败 {tool_name}: {e}")
            raise
    
    async def execute_command(self, command_name: str, **kwargs) -> Any:
        """执行命令"""
        try:
            if command_name not in self.registered_commands:
                raise ValueError(f"未找到命令: {command_name}")
            
            plugin_name, cmd_func = self.registered_commands[command_name]
            
            # 检查插件是否激活
            plugin = self.plugin_loader.get_plugin(plugin_name)
            if not plugin or not plugin.is_active():
                raise ValueError(f"插件未激活: {plugin_name}")
            
            # 执行命令
            result = await cmd_func(**kwargs)
            
            # 发出事件
            await self.emit_event(EventType.COMMAND_EXECUTED, plugin_name, {
                "command_name": command_name,
                "kwargs": kwargs,
                "result": result
            })
            
            return result
            
        except Exception as e:
            logger.error(f"执行命令失败 {command_name}: {e}")
            raise
    
    async def trigger_hooks(self, event_name: str, **kwargs) -> List[Any]:
        """触发钩子"""
        results = []
        
        hooks = self.registered_hooks.get(event_name, [])
        for plugin_name, hook_func in hooks:
            try:
                # 检查插件是否激活
                plugin = self.plugin_loader.get_plugin(plugin_name)
                if not plugin or not plugin.is_active():
                    continue
                
                result = await hook_func(**kwargs)
                results.append(result)
                
            except Exception as e:
                logger.error(f"钩子执行失败 {plugin_name}.{event_name}: {e}")
        
        return results
    
    def get_plugin_status(self) -> Dict[str, Any]:
        """获取插件状态"""
        plugins = self.plugin_loader.list_plugins()
        
        return {
            "total_plugins": len(plugins),
            "loaded_plugins": sum(1 for p in plugins.values() if p["loaded"]),
            "active_plugins": sum(1 for p in plugins.values() if p["active"]),
            "registered_tools": len(self.registered_tools),
            "registered_commands": len(self.registered_commands),
            "registered_hooks": sum(len(hooks) for hooks in self.registered_hooks.values()),
            "plugins": plugins
        }
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return list(self.registered_tools.keys())
    
    def get_available_commands(self) -> List[str]:
        """获取可用命令列表"""
        return list(self.registered_commands.keys())
    
    def get_event_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取事件历史"""
        recent_events = self.event_history[-limit:] if limit > 0 else self.event_history
        return [asdict(event) for event in recent_events]
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停用所有插件
            active_plugins = self.plugin_loader.get_active_plugins()
            for plugin in active_plugins:
                await self.deactivate_plugin(plugin.info.name)
            
            # 卸载所有插件
            for plugin_name in list(self.plugin_loader.loaded_plugins.keys()):
                await self.unload_plugin(plugin_name)
            
            logger.info("插件管理器清理完成")
            
        except Exception as e:
            logger.error(f"插件管理器清理失败: {e}")


# 全局插件管理器实例
plugin_manager = PluginManager()


def get_plugin_manager() -> PluginManager:
    """获取全局插件管理器"""
    return plugin_manager
