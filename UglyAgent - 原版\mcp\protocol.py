#!/usr/bin/env python3
"""
UglyAgent MCP协议处理模块
实现完整的MCP协议标准
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class MCPMessageType(Enum):
    """MCP消息类型"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"


class MCPMethod(Enum):
    """MCP方法枚举"""
    INITIALIZE = "initialize"
    INITIALIZED = "initialized"
    TOOLS_LIST = "tools/list"
    TOOLS_CALL = "tools/call"
    RESOURCES_LIST = "resources/list"
    RESOURCES_READ = "resources/read"
    PROMPTS_LIST = "prompts/list"
    PROMPTS_GET = "prompts/get"
    COMPLETION_COMPLETE = "completion/complete"
    LOGGING_SET_LEVEL = "logging/setLevel"


@dataclass
class MCPError:
    """MCP错误类"""
    code: int
    message: str
    data: Optional[Dict[str, Any]] = None


@dataclass
class MCPRequest:
    """MCP请求类"""
    jsonrpc: str = "2.0"
    id: Optional[Union[str, int]] = None
    method: str = ""
    params: Optional[Dict[str, Any]] = None


@dataclass
class MCPResponse:
    """MCP响应类"""
    jsonrpc: str = "2.0"
    id: Optional[Union[str, int]] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[MCPError] = None


@dataclass
class MCPNotification:
    """MCP通知类"""
    jsonrpc: str = "2.0"
    method: str = ""
    params: Optional[Dict[str, Any]] = None


@dataclass
class ToolDefinition:
    """工具定义类"""
    name: str
    description: str
    inputSchema: Dict[str, Any]


@dataclass
class ResourceDefinition:
    """资源定义类"""
    uri: str
    name: str
    description: Optional[str] = None
    mimeType: Optional[str] = None


@dataclass
class PromptDefinition:
    """提示定义类"""
    name: str
    description: str
    arguments: Optional[List[Dict[str, Any]]] = None


class MCPProtocolHandler:
    """MCP协议处理器"""
    
    def __init__(self):
        self.server_info = {
            "name": "UglyAgent",
            "version": "1.0.0",
            "description": "超越Augment的AI Agent"
        }
        self.client_info: Optional[Dict[str, Any]] = None
        self.capabilities = {
            "tools": {},
            "resources": {},
            "prompts": {},
            "completion": {},
            "logging": {}
        }
        self.tools: Dict[str, ToolDefinition] = {}
        self.resources: Dict[str, ResourceDefinition] = {}
        self.prompts: Dict[str, PromptDefinition] = {}
        self.handlers: Dict[str, Callable] = {}
        self.initialized = False
        
        # 注册默认处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认处理器"""
        self.handlers.update({
            MCPMethod.INITIALIZE.value: self._handle_initialize,
            MCPMethod.INITIALIZED.value: self._handle_initialized,
            MCPMethod.TOOLS_LIST.value: self._handle_tools_list,
            MCPMethod.TOOLS_CALL.value: self._handle_tools_call,
            MCPMethod.RESOURCES_LIST.value: self._handle_resources_list,
            MCPMethod.RESOURCES_READ.value: self._handle_resources_read,
            MCPMethod.PROMPTS_LIST.value: self._handle_prompts_list,
            MCPMethod.PROMPTS_GET.value: self._handle_prompts_get,
            MCPMethod.COMPLETION_COMPLETE.value: self._handle_completion_complete,
            MCPMethod.LOGGING_SET_LEVEL.value: self._handle_logging_set_level
        })
    
    def register_tool(self, tool: ToolDefinition, handler: Callable):
        """注册工具"""
        self.tools[tool.name] = tool
        self.handlers[f"tool_{tool.name}"] = handler
        logger.info(f"已注册工具: {tool.name}")
    
    def register_resource(self, resource: ResourceDefinition, handler: Callable):
        """注册资源"""
        self.resources[resource.uri] = resource
        self.handlers[f"resource_{resource.uri}"] = handler
        logger.info(f"已注册资源: {resource.uri}")
    
    def register_prompt(self, prompt: PromptDefinition, handler: Callable):
        """注册提示"""
        self.prompts[prompt.name] = prompt
        self.handlers[f"prompt_{prompt.name}"] = handler
        logger.info(f"已注册提示: {prompt.name}")
    
    def parse_message(self, message: str) -> Union[MCPRequest, MCPResponse, MCPNotification]:
        """解析MCP消息"""
        try:
            data = json.loads(message)
            
            if "method" in data and "id" in data:
                # 请求消息
                return MCPRequest(
                    jsonrpc=data.get("jsonrpc", "2.0"),
                    id=data.get("id"),
                    method=data.get("method", ""),
                    params=data.get("params")
                )
            elif "result" in data or "error" in data:
                # 响应消息
                error = None
                if "error" in data:
                    error_data = data["error"]
                    error = MCPError(
                        code=error_data.get("code", -1),
                        message=error_data.get("message", ""),
                        data=error_data.get("data")
                    )
                
                return MCPResponse(
                    jsonrpc=data.get("jsonrpc", "2.0"),
                    id=data.get("id"),
                    result=data.get("result"),
                    error=error
                )
            elif "method" in data:
                # 通知消息
                return MCPNotification(
                    jsonrpc=data.get("jsonrpc", "2.0"),
                    method=data.get("method", ""),
                    params=data.get("params")
                )
            else:
                raise ValueError("无效的MCP消息格式")
                
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析错误: {e}")
        except Exception as e:
            raise ValueError(f"消息解析错误: {e}")
    
    def create_response(self, request_id: Union[str, int], result: Dict[str, Any]) -> str:
        """创建成功响应"""
        response = MCPResponse(id=request_id, result=result)
        return json.dumps(asdict(response), ensure_ascii=False)
    
    def create_error_response(self, request_id: Union[str, int], error: MCPError) -> str:
        """创建错误响应"""
        response = MCPResponse(id=request_id, error=error)
        return json.dumps(asdict(response), ensure_ascii=False)
    
    def create_notification(self, method: str, params: Optional[Dict[str, Any]] = None) -> str:
        """创建通知"""
        notification = MCPNotification(method=method, params=params)
        return json.dumps(asdict(notification), ensure_ascii=False)
    
    async def handle_message(self, message: str) -> Optional[str]:
        """处理MCP消息"""
        try:
            parsed_message = self.parse_message(message)
            
            if isinstance(parsed_message, MCPRequest):
                return await self._handle_request(parsed_message)
            elif isinstance(parsed_message, MCPNotification):
                await self._handle_notification(parsed_message)
                return None
            else:
                logger.warning(f"收到意外的消息类型: {type(parsed_message)}")
                return None
                
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            error = MCPError(code=-32603, message=f"内部错误: {str(e)}")
            return self.create_error_response(None, error)
    
    async def _handle_request(self, request: MCPRequest) -> str:
        """处理请求"""
        try:
            handler = self.handlers.get(request.method)
            if not handler:
                error = MCPError(code=-32601, message=f"未知方法: {request.method}")
                return self.create_error_response(request.id, error)
            
            result = await handler(request.params or {})
            return self.create_response(request.id, result)
            
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            error = MCPError(code=-32603, message=f"内部错误: {str(e)}")
            return self.create_error_response(request.id, error)
    
    async def _handle_notification(self, notification: MCPNotification):
        """处理通知"""
        try:
            handler = self.handlers.get(notification.method)
            if handler:
                await handler(notification.params or {})
            else:
                logger.warning(f"未知通知方法: {notification.method}")
                
        except Exception as e:
            logger.error(f"处理通知失败: {e}")
    
    async def _handle_initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        self.client_info = params.get("clientInfo")
        
        return {
            "protocolVersion": "2024-11-05",
            "capabilities": self.capabilities,
            "serverInfo": self.server_info
        }
    
    async def _handle_initialized(self, params: Dict[str, Any]):
        """处理初始化完成通知"""
        self.initialized = True
        logger.info("MCP协议初始化完成")
    
    async def _handle_tools_list(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具列表请求"""
        tools = []
        for tool in self.tools.values():
            tools.append(asdict(tool))
        
        return {"tools": tools}
    
    async def _handle_tools_call(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具调用请求"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if not tool_name:
            raise ValueError("工具名称不能为空")
        
        if tool_name not in self.tools:
            raise ValueError(f"未知工具: {tool_name}")
        
        handler = self.handlers.get(f"tool_{tool_name}")
        if not handler:
            raise ValueError(f"工具 {tool_name} 没有处理器")
        
        result = await handler(arguments)
        
        return {
            "content": [{
                "type": "text",
                "text": json.dumps(result, ensure_ascii=False, indent=2)
            }],
            "isError": not result.get("success", True)
        }
    
    async def _handle_resources_list(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理资源列表请求"""
        resources = []
        for resource in self.resources.values():
            resources.append(asdict(resource))
        
        return {"resources": resources}
    
    async def _handle_resources_read(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理资源读取请求"""
        uri = params.get("uri")
        
        if not uri:
            raise ValueError("资源URI不能为空")
        
        if uri not in self.resources:
            raise ValueError(f"未知资源: {uri}")
        
        handler = self.handlers.get(f"resource_{uri}")
        if not handler:
            raise ValueError(f"资源 {uri} 没有处理器")
        
        result = await handler(params)
        
        return {
            "contents": [{
                "uri": uri,
                "mimeType": self.resources[uri].mimeType or "text/plain",
                "text": result
            }]
        }
    
    async def _handle_prompts_list(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理提示列表请求"""
        prompts = []
        for prompt in self.prompts.values():
            prompts.append(asdict(prompt))
        
        return {"prompts": prompts}
    
    async def _handle_prompts_get(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理提示获取请求"""
        name = params.get("name")
        arguments = params.get("arguments", {})
        
        if not name:
            raise ValueError("提示名称不能为空")
        
        if name not in self.prompts:
            raise ValueError(f"未知提示: {name}")
        
        handler = self.handlers.get(f"prompt_{name}")
        if not handler:
            raise ValueError(f"提示 {name} 没有处理器")
        
        result = await handler(arguments)
        
        return {
            "messages": [{
                "role": "user",
                "content": {
                    "type": "text",
                    "text": result
                }
            }]
        }
    
    async def _handle_completion_complete(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理补全请求"""
        # 这里可以实现代码补全功能
        return {"completion": {"values": []}}
    
    async def _handle_logging_set_level(self, params: Dict[str, Any]):
        """处理日志级别设置"""
        level = params.get("level", "INFO")
        logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        logger.info(f"日志级别已设置为: {level}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取协议状态"""
        return {
            "initialized": self.initialized,
            "client_info": self.client_info,
            "server_info": self.server_info,
            "tools_count": len(self.tools),
            "resources_count": len(self.resources),
            "prompts_count": len(self.prompts)
        }
