# UglyAgent 大模型配置文件
# 复制此文件为 models.yaml 并填入你的API密钥

# 默认使用的模型
default_model: "openai_gpt4"

# 模型配置
models:
  # OpenAI 模型
  openai_gpt4:
    provider: "openai"
    model_name: "gpt-4"
    api_key: "your_openai_api_key_here"
    api_base: "https://api.openai.com/v1"
    max_tokens: 4096
    temperature: 0.7
    timeout: 60
    retry_attempts: 3
    
  openai_gpt35:
    provider: "openai"
    model_name: "gpt-3.5-turbo"
    api_key: "your_openai_api_key_here"
    api_base: "https://api.openai.com/v1"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30
    retry_attempts: 3

  # Claude 模型
  claude_sonnet:
    provider: "anthropic"
    model_name: "claude-3-sonnet-20240229"
    api_key: "your_anthropic_api_key_here"
    api_base: "https://api.anthropic.com"
    max_tokens: 4096
    temperature: 0.7
    timeout: 60
    retry_attempts: 3
    
  claude_haiku:
    provider: "anthropic"
    model_name: "claude-3-haiku-20240307"
    api_key: "your_anthropic_api_key_here"
    api_base: "https://api.anthropic.com"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30
    retry_attempts: 3

  # 本地模型 (Ollama)
  local_llama:
    provider: "ollama"
    model_name: "llama2"
    api_base: "http://localhost:11434"
    max_tokens: 4096
    temperature: 0.7
    timeout: 120
    retry_attempts: 2
    
  local_codellama:
    provider: "ollama"
    model_name: "codellama"
    api_base: "http://localhost:11434"
    max_tokens: 4096
    temperature: 0.3
    timeout: 120
    retry_attempts: 2

  # 自定义API
  custom_model:
    provider: "custom"
    model_name: "custom-model"
    api_key: "your_custom_api_key"
    api_base: "https://your-custom-api.com/v1"
    headers:
      "Custom-Header": "value"
    max_tokens: 4096
    temperature: 0.7
    timeout: 60
    retry_attempts: 3

# 负载均衡配置
load_balancing:
  enabled: true
  strategy: "round_robin"  # round_robin, random, least_used
  fallback_models:
    - "openai_gpt35"
    - "claude_haiku"
    - "local_llama"

# 成本优化配置
cost_optimization:
  enabled: true
  max_cost_per_request: 0.1  # 美元
  prefer_cheaper_models: true
  cost_tracking: true
  
# 模型性能配置
performance:
  # 并发请求限制
  max_concurrent_requests: 5
  # 请求队列大小
  request_queue_size: 100
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600  # 缓存时间(秒)
    max_size: 1000  # 最大缓存条目数

# 安全配置
security:
  # API密钥加密
  encrypt_api_keys: true
  # 请求日志
  log_requests: false
  # 敏感信息过滤
  filter_sensitive_data: true
  
# 监控配置
monitoring:
  enabled: true
  metrics:
    - "response_time"
    - "token_usage"
    - "error_rate"
    - "cost"
  alerts:
    high_error_rate: 0.1
    high_cost: 10.0  # 每小时美元
    slow_response: 30.0  # 秒
