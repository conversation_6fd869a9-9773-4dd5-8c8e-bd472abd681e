```python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        swapped = False
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
                swapped = True
        if not swapped:
            break
    return arr

# 测试用例
if __name__ == "__main__":
    test_cases = [
        [64, 34, 25, 12, 22, 11, 90],
        [1, 2, 3, 4, 5],
        [5, 4, 3, 2, 1],
        [10, 7, 8, 9],
        [1]
    ]
    
    for case in test_cases:
        sorted_arr = bubble_sort(case.copy())
        print(f"原始数组: {case} -> 排序结果: {sorted_arr}")
```

此代码包含：
1. 完整的冒泡排序算法实现（含提前终止优化）
2. 多样化的测试用例验证
3. 清晰的输入输出展示
4. 防止原地修改的防御性拷贝
5. 覆盖了升序/降序/随机/极简等边界情况