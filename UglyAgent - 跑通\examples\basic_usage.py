#!/usr/bin/env python3
"""
UglyAgent 基础使用示例
演示UglyAgent的基本功能和用法
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from UglyAgent.core.agent import UglyAgent


async def basic_chat_example():
    """基础对话示例"""
    print("🤖 基础对话示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 简单对话
        response = await agent.chat("你好，请介绍一下自己")
        print(f"Agent: {response['response']}")
        
        # 询问能力
        response = await agent.chat("你能做什么？")
        print(f"Agent: {response['response']}")
        
        # 数学计算
        response = await agent.chat("计算 123 * 456 等于多少？")
        print(f"Agent: {response['response']}")


async def file_operations_example():
    """文件操作示例"""
    print("\n📁 文件操作示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 创建示例文件
        test_content = """# 示例Python文件
def hello_world():
    print("Hello, World!")
    return "success"

def calculate(a, b):
    return a + b

if __name__ == "__main__":
    hello_world()
    result = calculate(10, 20)
    print(f"Result: {result}")
"""
        
        # 写入文件
        response = await agent.chat(
            f"创建一个文件 example.py，内容如下：\n{test_content}"
        )
        print(f"创建文件: {response['success']}")
        
        # 读取文件
        response = await agent.chat("读取文件 example.py 的内容")
        print(f"读取文件: {response['success']}")
        
        # 分析代码
        response = await agent.chat("分析 example.py 的代码结构")
        print(f"代码分析: {response['success']}")


async def web_tools_example():
    """网络工具示例"""
    print("\n🌐 网络工具示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 检查URL状态
        response = await agent.chat("检查 https://httpbin.org/status/200 的状态")
        print(f"URL检查: {response['success']}")
        
        # 简单HTTP请求
        response = await agent.chat("发送GET请求到 https://httpbin.org/json")
        print(f"HTTP请求: {response['success']}")


async def system_info_example():
    """系统信息示例"""
    print("\n💻 系统信息示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 获取系统信息
        response = await agent.chat("获取系统的CPU和内存信息")
        print(f"系统信息: {response['success']}")
        
        # 检查磁盘空间
        response = await agent.chat("检查磁盘空间使用情况")
        print(f"磁盘空间: {response['success']}")


async def task_planning_example():
    """任务规划示例"""
    print("\n📋 任务规划示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 复杂任务 - 会自动使用任务规划
        response = await agent.chat(
            "帮我做一个完整的项目分析："
            "1. 列出当前目录的所有Python文件"
            "2. 分析每个文件的代码质量"
            "3. 检查语法错误"
            "4. 生成分析报告"
        )
        
        print(f"任务规划执行: {response['success']}")
        if response.get('used_planning'):
            execution_result = response.get('execution_result', {})
            print(f"完成任务数: {execution_result.get('completed_tasks', 0)}")
            print(f"执行时间: {execution_result.get('execution_time', 0):.2f}秒")


async def memory_example():
    """记忆系统示例"""
    print("\n🧠 记忆系统示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 第一轮对话
        response = await agent.chat("我的名字是张三，我是一名Python开发者")
        print(f"记录信息: {response['success']}")
        
        # 第二轮对话 - 测试记忆
        response = await agent.chat("你还记得我的名字和职业吗？")
        print(f"回忆信息: {response['response']}")
        
        # 查看对话历史
        history = agent.get_conversation_history(limit=5)
        print(f"对话历史条数: {len(history)}")


async def error_handling_example():
    """错误处理示例"""
    print("\n⚠️ 错误处理示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 尝试读取不存在的文件
        response = await agent.chat("读取文件 nonexistent.txt")
        print(f"处理不存在文件: {response['success']}")
        if not response['success']:
            print(f"错误信息: {response.get('error', 'Unknown error')}")
        
        # 尝试访问无效URL
        response = await agent.chat("访问 http://invalid-url-12345.com")
        print(f"处理无效URL: {response['success']}")
        if not response['success']:
            print(f"错误信息: {response.get('error', 'Unknown error')}")


async def agent_status_example():
    """Agent状态示例"""
    print("\n📊 Agent状态示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 获取Agent状态
        status = agent.get_agent_status()
        
        print(f"会话ID: {status.get('session_id')}")
        print(f"会话激活: {status.get('session_active')}")
        print(f"对话长度: {status.get('conversation_length')}")
        print(f"可用模型数: {len(status.get('available_models', []))}")
        
        # 记忆统计
        memory_stats = status.get('memory_stats', {})
        print(f"短期记忆: {memory_stats.get('short_term_count', 0)}")
        print(f"长期记忆: {memory_stats.get('long_term_count', 0)}")


async def custom_model_example():
    """自定义模型示例"""
    print("\n🎯 自定义模型示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 使用特定模型
        response = await agent.chat(
            "用简洁的方式解释什么是人工智能",
            model="openai_gpt35"  # 指定使用GPT-3.5
        )
        print(f"GPT-3.5响应: {response['response'][:100]}...")
        
        # 使用系统提示
        response = await agent.chat(
            "介绍Python编程语言",
            system_prompt="你是一个专业的编程教师，请用通俗易懂的语言解释。"
        )
        print(f"专业教师模式: {response['response'][:100]}...")


async def batch_operations_example():
    """批量操作示例"""
    print("\n🔄 批量操作示例")
    print("=" * 50)
    
    async with UglyAgent() as agent:
        # 批量文件操作
        files_to_create = [
            ("test1.py", "print('Hello from test1')"),
            ("test2.py", "print('Hello from test2')"),
            ("test3.py", "print('Hello from test3')")
        ]
        
        for filename, content in files_to_create:
            response = await agent.chat(f"创建文件 {filename}，内容：{content}")
            print(f"创建 {filename}: {response['success']}")
        
        # 批量分析
        response = await agent.chat("分析所有test*.py文件的代码质量")
        print(f"批量分析: {response['success']}")


async def main():
    """主函数 - 运行所有示例"""
    print("🚀 UglyAgent 使用示例")
    print("=" * 80)
    
    examples = [
        basic_chat_example,
        file_operations_example,
        web_tools_example,
        system_info_example,
        memory_example,
        agent_status_example,
        custom_model_example,
        error_handling_example,
        # task_planning_example,  # 注释掉，因为可能耗时较长
        # batch_operations_example,  # 注释掉，避免创建太多文件
    ]
    
    for i, example in enumerate(examples, 1):
        try:
            print(f"\n[{i}/{len(examples)}] 运行示例: {example.__name__}")
            await example()
            print("✅ 示例完成")
        except Exception as e:
            print(f"❌ 示例失败: {e}")
        
        # 添加分隔符
        if i < len(examples):
            print("\n" + "-" * 80)
    
    print("\n🎉 所有示例运行完成！")
    print("\n💡 提示：")
    print("- 确保已配置API密钥 (config/models.yaml)")
    print("- 某些示例需要网络连接")
    print("- 查看日志文件了解详细信息")


if __name__ == "__main__":
    # 运行示例
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 示例被用户中断")
    except Exception as e:
        print(f"\n❌ 运行失败: {e}")
        print("请检查配置和依赖是否正确安装")
