#!/usr/bin/env python3
"""
UglyAgent 文件操作工具
提供强大的文件和目录操作功能
"""

import os
import shutil
import glob
import mimetypes
import hashlib
import json
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import logging
import re
import chardet

from .base import BaseTool, ToolResult, ToolCategory

logger = logging.getLogger(__name__)


class FileOperationTools:
    """文件操作工具集合"""
    
    def __init__(self):
        self.supported_encodings = ['utf-8', 'gbk', 'gb2312', 'ascii', 'latin-1']
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.safe_extensions = {
            '.txt', '.py', '.js', '.html', '.css', '.json', '.yaml', '.yml',
            '.md', '.rst', '.xml', '.csv', '.log', '.ini', '.cfg', '.conf'
        }
    
    async def read_file(self, file_path: str, encoding: str = 'utf-8', 
                       detect_encoding: bool = True) -> Dict[str, Any]:
        """读取文件内容"""
        try:
            path = Path(file_path)
            
            # 检查文件是否存在
            if not path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size > self.max_file_size:
                return {
                    "success": False,
                    "error": f"文件过大: {file_size} bytes (最大: {self.max_file_size} bytes)"
                }
            
            # 检测编码
            if detect_encoding:
                with open(path, 'rb') as f:
                    raw_data = f.read(10000)  # 读取前10KB检测编码
                    detected = chardet.detect(raw_data)
                    if detected['confidence'] > 0.7:
                        encoding = detected['encoding']
            
            # 读取文件内容
            with open(path, 'r', encoding=encoding) as f:
                content = f.read()
            
            # 获取文件信息
            stat = path.stat()
            mime_type, _ = mimetypes.guess_type(str(path))
            
            return {
                "success": True,
                "content": content,
                "file_path": str(path.absolute()),
                "size": file_size,
                "encoding": encoding,
                "mime_type": mime_type,
                "line_count": len(content.splitlines()),
                "modified_time": stat.st_mtime,
                "created_time": stat.st_ctime
            }
            
        except UnicodeDecodeError as e:
            return {
                "success": False,
                "error": f"编码错误: {e}. 尝试使用不同的编码或启用编码检测"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"读取文件失败: {e}"
            }
    
    async def write_file(self, file_path: str, content: str, 
                        encoding: str = 'utf-8', backup: bool = True) -> Dict[str, Any]:
        """写入文件内容"""
        try:
            path = Path(file_path)
            
            # 创建目录
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份原文件
            backup_path = None
            if backup and path.exists():
                backup_path = path.with_suffix(path.suffix + '.backup')
                shutil.copy2(path, backup_path)
            
            # 写入文件
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
            
            # 获取文件信息
            stat = path.stat()
            
            return {
                "success": True,
                "file_path": str(path.absolute()),
                "size": stat.st_size,
                "encoding": encoding,
                "backup_path": str(backup_path) if backup_path else None,
                "line_count": len(content.splitlines())
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"写入文件失败: {e}"
            }
    
    async def list_directory(self, directory_path: str, recursive: bool = False,
                           include_hidden: bool = False, 
                           pattern: str = None) -> Dict[str, Any]:
        """列出目录内容"""
        try:
            path = Path(directory_path)
            
            if not path.exists():
                return {
                    "success": False,
                    "error": f"目录不存在: {directory_path}"
                }
            
            if not path.is_dir():
                return {
                    "success": False,
                    "error": f"不是目录: {directory_path}"
                }
            
            items = []
            
            if recursive:
                # 递归遍历
                glob_pattern = "**/*" if not pattern else f"**/{pattern}"
                for item_path in path.glob(glob_pattern):
                    if not include_hidden and item_path.name.startswith('.'):
                        continue
                    
                    items.append(self._get_item_info(item_path))
            else:
                # 非递归遍历
                for item_path in path.iterdir():
                    if not include_hidden and item_path.name.startswith('.'):
                        continue
                    
                    if pattern and not item_path.match(pattern):
                        continue
                    
                    items.append(self._get_item_info(item_path))
            
            # 排序
            items.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
            
            return {
                "success": True,
                "directory": str(path.absolute()),
                "items": items,
                "count": len(items)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"列出目录失败: {e}"
            }
    
    def _get_item_info(self, path: Path) -> Dict[str, Any]:
        """获取文件/目录信息"""
        try:
            stat = path.stat()
            mime_type, _ = mimetypes.guess_type(str(path))
            
            return {
                "name": path.name,
                "path": str(path.absolute()),
                "type": "directory" if path.is_dir() else "file",
                "size": stat.st_size if path.is_file() else None,
                "mime_type": mime_type,
                "modified_time": stat.st_mtime,
                "created_time": stat.st_ctime,
                "permissions": oct(stat.st_mode)[-3:]
            }
        except Exception as e:
            return {
                "name": path.name,
                "path": str(path.absolute()),
                "type": "unknown",
                "error": str(e)
            }
    
    async def search_files(self, pattern: str, directory: str = ".", 
                          file_types: List[str] = None,
                          case_sensitive: bool = False,
                          max_results: int = 1000) -> Dict[str, Any]:
        """搜索文件内容"""
        try:
            path = Path(directory)
            
            if not path.exists():
                return {
                    "success": False,
                    "error": f"目录不存在: {directory}"
                }
            
            results = []
            search_count = 0
            
            # 编译正则表达式
            flags = 0 if case_sensitive else re.IGNORECASE
            regex = re.compile(pattern, flags)
            
            # 确定搜索的文件类型
            if file_types:
                extensions = [ext if ext.startswith('.') else f'.{ext}' for ext in file_types]
            else:
                extensions = list(self.safe_extensions)
            
            # 搜索文件
            for file_path in path.rglob("*"):
                if search_count >= max_results:
                    break
                
                if not file_path.is_file():
                    continue
                
                if file_path.suffix.lower() not in extensions:
                    continue
                
                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 搜索匹配
                    matches = []
                    for line_num, line in enumerate(content.splitlines(), 1):
                        if regex.search(line):
                            matches.append({
                                "line_number": line_num,
                                "line_content": line.strip(),
                                "match_positions": [m.span() for m in regex.finditer(line)]
                            })
                    
                    if matches:
                        results.append({
                            "file_path": str(file_path.absolute()),
                            "matches": matches,
                            "match_count": len(matches)
                        })
                        search_count += 1
                
                except Exception as e:
                    logger.warning(f"搜索文件 {file_path} 时出错: {e}")
                    continue
            
            return {
                "success": True,
                "pattern": pattern,
                "directory": str(path.absolute()),
                "results": results,
                "total_matches": sum(r["match_count"] for r in results),
                "files_found": len(results),
                "search_truncated": search_count >= max_results
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"搜索文件失败: {e}"
            }
    
    async def copy_file(self, source: str, destination: str, 
                       overwrite: bool = False) -> Dict[str, Any]:
        """复制文件"""
        try:
            src_path = Path(source)
            dst_path = Path(destination)
            
            if not src_path.exists():
                return {
                    "success": False,
                    "error": f"源文件不存在: {source}"
                }
            
            if dst_path.exists() and not overwrite:
                return {
                    "success": False,
                    "error": f"目标文件已存在: {destination}"
                }
            
            # 创建目标目录
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(src_path, dst_path)
            
            return {
                "success": True,
                "source": str(src_path.absolute()),
                "destination": str(dst_path.absolute()),
                "size": dst_path.stat().st_size
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"复制文件失败: {e}"
            }
    
    async def move_file(self, source: str, destination: str) -> Dict[str, Any]:
        """移动文件"""
        try:
            src_path = Path(source)
            dst_path = Path(destination)
            
            if not src_path.exists():
                return {
                    "success": False,
                    "error": f"源文件不存在: {source}"
                }
            
            # 创建目标目录
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 移动文件
            shutil.move(str(src_path), str(dst_path))
            
            return {
                "success": True,
                "source": source,
                "destination": str(dst_path.absolute())
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"移动文件失败: {e}"
            }
    
    async def delete_file(self, file_path: str, force: bool = False) -> Dict[str, Any]:
        """删除文件"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 安全检查
            if not force and path.suffix.lower() not in self.safe_extensions:
                return {
                    "success": False,
                    "error": f"不安全的文件类型: {path.suffix}. 使用 force=True 强制删除"
                }
            
            # 删除文件
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
            
            return {
                "success": True,
                "deleted_path": str(path.absolute())
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"删除文件失败: {e}"
            }
    
    async def get_file_hash(self, file_path: str, 
                           algorithm: str = 'md5') -> Dict[str, Any]:
        """计算文件哈希值"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 支持的哈希算法
            hash_algorithms = {
                'md5': hashlib.md5,
                'sha1': hashlib.sha1,
                'sha256': hashlib.sha256,
                'sha512': hashlib.sha512
            }
            
            if algorithm not in hash_algorithms:
                return {
                    "success": False,
                    "error": f"不支持的哈希算法: {algorithm}"
                }
            
            # 计算哈希值
            hash_obj = hash_algorithms[algorithm]()
            
            with open(path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            
            return {
                "success": True,
                "file_path": str(path.absolute()),
                "algorithm": algorithm,
                "hash": hash_obj.hexdigest(),
                "size": path.stat().st_size
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"计算文件哈希失败: {e}"
            }
