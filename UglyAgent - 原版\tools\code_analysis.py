#!/usr/bin/env python3
"""
UglyAgent 代码分析工具
提供代码结构分析、复杂度计算、语法检查等功能
"""

import ast
import re
import os
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class CodeAnalysisTools:
    """代码分析工具集合"""
    
    def __init__(self):
        self.supported_languages = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby'
        }
    
    async def analyze_code(self, file_path: str, language: str = None) -> Dict[str, Any]:
        """分析代码文件"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 检测语言
            if not language:
                language = self.supported_languages.get(path.suffix.lower())
            
            if not language:
                return {
                    "success": False,
                    "error": f"不支持的文件类型: {path.suffix}"
                }
            
            # 读取文件内容
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基础统计
            lines = content.splitlines()
            basic_stats = self._get_basic_stats(lines)
            
            # 语言特定分析
            if language == 'python':
                analysis = await self._analyze_python_code(content, path)
            elif language in ['javascript', 'typescript']:
                analysis = await self._analyze_js_code(content, path)
            else:
                analysis = await self._analyze_generic_code(content, path, language)
            
            # 合并结果
            result = {
                "success": True,
                "file_path": str(path.absolute()),
                "language": language,
                "basic_stats": basic_stats,
                **analysis
            }
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": f"代码分析失败: {e}"
            }
    
    def _get_basic_stats(self, lines: List[str]) -> Dict[str, Any]:
        """获取基础统计信息"""
        total_lines = len(lines)
        blank_lines = sum(1 for line in lines if not line.strip())
        comment_lines = 0
        code_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
            elif stripped.startswith('#') or stripped.startswith('//') or stripped.startswith('/*'):
                comment_lines += 1
            else:
                code_lines += 1
        
        return {
            "total_lines": total_lines,
            "code_lines": code_lines,
            "blank_lines": blank_lines,
            "comment_lines": comment_lines,
            "comment_ratio": comment_lines / max(code_lines, 1)
        }
    
    async def _analyze_python_code(self, content: str, path: Path) -> Dict[str, Any]:
        """分析Python代码"""
        try:
            # 解析AST
            tree = ast.parse(content)
            
            # 提取信息
            functions = []
            classes = []
            imports = []
            complexity_score = 0
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = self._analyze_python_function(node, content)
                    functions.append(func_info)
                    complexity_score += func_info.get('complexity', 1)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = self._analyze_python_class(node, content)
                    classes.append(class_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    import_info = self._analyze_python_import(node)
                    imports.append(import_info)
            
            # 计算质量指标
            quality_metrics = self._calculate_python_quality(tree, content)
            
            return {
                "functions": functions,
                "classes": classes,
                "imports": imports,
                "complexity_score": complexity_score,
                "quality_metrics": quality_metrics,
                "issues": self._find_python_issues(tree, content)
            }
            
        except SyntaxError as e:
            return {
                "syntax_error": {
                    "line": e.lineno,
                    "column": e.offset,
                    "message": e.msg
                },
                "functions": [],
                "classes": [],
                "imports": []
            }
    
    def _analyze_python_function(self, node: ast.FunctionDef, content: str) -> Dict[str, Any]:
        """分析Python函数"""
        # 计算圈复杂度
        complexity = 1  # 基础复杂度
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        # 获取参数信息
        args = []
        for arg in node.args.args:
            args.append({
                "name": arg.arg,
                "annotation": ast.unparse(arg.annotation) if arg.annotation else None
            })
        
        # 获取返回类型
        return_type = ast.unparse(node.returns) if node.returns else None
        
        # 获取文档字符串
        docstring = ast.get_docstring(node)
        
        return {
            "name": node.name,
            "line_number": node.lineno,
            "arguments": args,
            "return_type": return_type,
            "complexity": complexity,
            "docstring": docstring,
            "is_async": isinstance(node, ast.AsyncFunctionDef),
            "decorators": [ast.unparse(d) for d in node.decorator_list]
        }
    
    def _analyze_python_class(self, node: ast.ClassDef, content: str) -> Dict[str, Any]:
        """分析Python类"""
        methods = []
        attributes = []
        
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                method_info = self._analyze_python_function(item, content)
                method_info["is_method"] = True
                methods.append(method_info)
            elif isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        attributes.append({
                            "name": target.id,
                            "line_number": item.lineno
                        })
        
        return {
            "name": node.name,
            "line_number": node.lineno,
            "methods": methods,
            "attributes": attributes,
            "base_classes": [ast.unparse(base) for base in node.bases],
            "docstring": ast.get_docstring(node),
            "decorators": [ast.unparse(d) for d in node.decorator_list]
        }
    
    def _analyze_python_import(self, node) -> Dict[str, Any]:
        """分析Python导入"""
        if isinstance(node, ast.Import):
            return {
                "type": "import",
                "modules": [alias.name for alias in node.names],
                "line_number": node.lineno
            }
        elif isinstance(node, ast.ImportFrom):
            return {
                "type": "from_import",
                "module": node.module,
                "names": [alias.name for alias in node.names],
                "line_number": node.lineno
            }
    
    def _calculate_python_quality(self, tree: ast.AST, content: str) -> Dict[str, Any]:
        """计算Python代码质量指标"""
        lines = content.splitlines()
        
        # 计算各种指标
        long_functions = 0
        complex_functions = 0
        undocumented_functions = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 函数长度
                func_lines = node.end_lineno - node.lineno + 1 if hasattr(node, 'end_lineno') else 0
                if func_lines > 50:
                    long_functions += 1
                
                # 复杂度
                complexity = 1
                for child in ast.walk(node):
                    if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                        complexity += 1
                
                if complexity > 10:
                    complex_functions += 1
                
                # 文档
                if not ast.get_docstring(node):
                    undocumented_functions += 1
        
        return {
            "long_functions": long_functions,
            "complex_functions": complex_functions,
            "undocumented_functions": undocumented_functions,
            "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0
        }
    
    def _find_python_issues(self, tree: ast.AST, content: str) -> List[Dict[str, Any]]:
        """查找Python代码问题"""
        issues = []
        
        for node in ast.walk(tree):
            # 检查空的except块
            if isinstance(node, ast.ExceptHandler):
                if len(node.body) == 1 and isinstance(node.body[0], ast.Pass):
                    issues.append({
                        "type": "empty_except",
                        "line": node.lineno,
                        "message": "空的except块"
                    })
            
            # 检查过长的函数
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno'):
                    func_length = node.end_lineno - node.lineno + 1
                    if func_length > 100:
                        issues.append({
                            "type": "long_function",
                            "line": node.lineno,
                            "message": f"函数过长: {func_length} 行"
                        })
        
        return issues
    
    async def _analyze_js_code(self, content: str, path: Path) -> Dict[str, Any]:
        """分析JavaScript/TypeScript代码"""
        # 简单的JavaScript分析
        functions = self._extract_js_functions(content)
        classes = self._extract_js_classes(content)
        imports = self._extract_js_imports(content)
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": imports,
            "complexity_score": len(functions) * 2 + len(classes) * 3
        }
    
    def _extract_js_functions(self, content: str) -> List[Dict[str, Any]]:
        """提取JavaScript函数"""
        functions = []
        
        # 匹配函数声明
        function_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*{'
        for match in re.finditer(function_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            functions.append({
                "name": match.group(1),
                "line_number": line_num,
                "type": "function_declaration"
            })
        
        # 匹配箭头函数
        arrow_pattern = r'(\w+)\s*=\s*\([^)]*\)\s*=>'
        for match in re.finditer(arrow_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            functions.append({
                "name": match.group(1),
                "line_number": line_num,
                "type": "arrow_function"
            })
        
        return functions
    
    def _extract_js_classes(self, content: str) -> List[Dict[str, Any]]:
        """提取JavaScript类"""
        classes = []
        
        class_pattern = r'class\s+(\w+)(?:\s+extends\s+(\w+))?\s*{'
        for match in re.finditer(class_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            classes.append({
                "name": match.group(1),
                "line_number": line_num,
                "extends": match.group(2)
            })
        
        return classes
    
    def _extract_js_imports(self, content: str) -> List[Dict[str, Any]]:
        """提取JavaScript导入"""
        imports = []
        
        # ES6 imports
        import_pattern = r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]'
        for match in re.finditer(import_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            imports.append({
                "type": "es6_import",
                "module": match.group(1),
                "line_number": line_num
            })
        
        # CommonJS requires
        require_pattern = r'require\([\'"]([^\'"]+)[\'"]\)'
        for match in re.finditer(require_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            imports.append({
                "type": "commonjs_require",
                "module": match.group(1),
                "line_number": line_num
            })
        
        return imports
    
    async def _analyze_generic_code(self, content: str, path: Path, language: str) -> Dict[str, Any]:
        """通用代码分析"""
        lines = content.splitlines()
        
        # 基础分析
        functions = self._count_functions_generic(content, language)
        complexity = self._estimate_complexity_generic(content)
        
        return {
            "functions": functions,
            "estimated_complexity": complexity,
            "language_specific": False
        }
    
    def _count_functions_generic(self, content: str, language: str) -> int:
        """通用函数计数"""
        patterns = {
            'java': r'(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\([^)]*\)\s*{',
            'cpp': r'\w+\s+\w+\s*\([^)]*\)\s*{',
            'c': r'\w+\s+\w+\s*\([^)]*\)\s*{',
            'csharp': r'(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\([^)]*\)\s*{',
            'go': r'func\s+\w+\s*\([^)]*\)\s*{',
            'rust': r'fn\s+\w+\s*\([^)]*\)\s*{',
            'php': r'function\s+\w+\s*\([^)]*\)\s*{',
            'ruby': r'def\s+\w+'
        }
        
        pattern = patterns.get(language, r'function\s+\w+')
        return len(re.findall(pattern, content))
    
    def _estimate_complexity_generic(self, content: str) -> int:
        """估算通用复杂度"""
        # 简单的复杂度估算
        complexity_keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'try', 'catch']
        complexity = 1
        
        for keyword in complexity_keywords:
            complexity += len(re.findall(rf'\b{keyword}\b', content, re.IGNORECASE))
        
        return complexity
    
    async def extract_functions(self, file_path: str) -> Dict[str, Any]:
        """提取代码中的函数定义"""
        analysis_result = await self.analyze_code(file_path)
        
        if not analysis_result["success"]:
            return analysis_result
        
        return {
            "success": True,
            "file_path": file_path,
            "functions": analysis_result.get("functions", []),
            "function_count": len(analysis_result.get("functions", []))
        }
    
    async def check_syntax(self, file_path: str, language: str = None) -> Dict[str, Any]:
        """检查代码语法"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 检测语言
            if not language:
                language = self.supported_languages.get(path.suffix.lower())
            
            if language == 'python':
                return await self._check_python_syntax(path)
            elif language in ['javascript', 'typescript']:
                return await self._check_js_syntax(path, language)
            else:
                return {
                    "success": True,
                    "message": f"语法检查暂不支持 {language} 语言",
                    "valid": True
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"语法检查失败: {e}"
            }
    
    async def _check_python_syntax(self, path: Path) -> Dict[str, Any]:
        """检查Python语法"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试编译
            compile(content, str(path), 'exec')
            
            return {
                "success": True,
                "valid": True,
                "message": "语法正确"
            }
            
        except SyntaxError as e:
            return {
                "success": True,
                "valid": False,
                "syntax_error": {
                    "line": e.lineno,
                    "column": e.offset,
                    "message": e.msg,
                    "text": e.text
                }
            }
    
    async def _check_js_syntax(self, path: Path, language: str) -> Dict[str, Any]:
        """检查JavaScript/TypeScript语法"""
        try:
            # 尝试使用node.js检查语法
            cmd = ['node', '-c', str(path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "valid": True,
                    "message": "语法正确"
                }
            else:
                return {
                    "success": True,
                    "valid": False,
                    "error": result.stderr
                }
                
        except FileNotFoundError:
            return {
                "success": True,
                "message": "Node.js未安装，无法检查JavaScript语法",
                "valid": True
            }
