#!/usr/bin/env python3
"""
UglyAgent Web工具
提供HTTP请求、网页抓取、API调用等功能
"""

import aiohttp
import asyncio
import json
import re
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse, parse_qs
import logging
from bs4 import BeautifulSoup
import time

logger = logging.getLogger(__name__)


class WebTools:
    """Web工具集合"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.default_headers = {
            'User-Agent': 'UglyAgent/1.0.0 (Web Tools)'
        }
        self.timeout = aiohttp.ClientTimeout(total=30)
        self.max_redirects = 10
        self.max_response_size = 10 * 1024 * 1024  # 10MB
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers=self.default_headers,
            timeout=self.timeout,
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def http_request(self, url: str, method: str = 'GET',
                          headers: Dict[str, str] = None,
                          data: Any = None,
                          params: Dict[str, str] = None,
                          json_data: Dict[str, Any] = None,
                          timeout: int = 30) -> Dict[str, Any]:
        """发送HTTP请求"""
        try:
            if not self.session:
                await self.__aenter__()
            
            # 准备请求参数
            request_headers = self.default_headers.copy()
            if headers:
                request_headers.update(headers)
            
            request_timeout = aiohttp.ClientTimeout(total=timeout)
            
            # 发送请求
            start_time = time.time()
            
            async with self.session.request(
                method=method.upper(),
                url=url,
                headers=request_headers,
                data=data,
                json=json_data,
                params=params,
                timeout=request_timeout,
                max_redirects=self.max_redirects
            ) as response:
                
                # 检查响应大小
                content_length = response.headers.get('Content-Length')
                if content_length and int(content_length) > self.max_response_size:
                    return {
                        "success": False,
                        "error": f"响应过大: {content_length} bytes"
                    }
                
                # 读取响应内容
                content = await response.read()
                
                # 检查实际大小
                if len(content) > self.max_response_size:
                    return {
                        "success": False,
                        "error": f"响应过大: {len(content)} bytes"
                    }
                
                response_time = time.time() - start_time
                
                # 尝试解析JSON
                json_content = None
                try:
                    if response.content_type == 'application/json':
                        json_content = await response.json()
                except:
                    pass
                
                # 解码文本内容
                text_content = None
                try:
                    text_content = content.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        text_content = content.decode('gbk')
                    except:
                        text_content = content.decode('utf-8', errors='ignore')
                
                return {
                    "success": True,
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "content": text_content,
                    "json": json_content,
                    "size": len(content),
                    "response_time": response_time,
                    "url": str(response.url),
                    "content_type": response.content_type
                }
                
        except aiohttp.ClientTimeout:
            return {
                "success": False,
                "error": f"请求超时 ({timeout}秒)"
            }
        except aiohttp.ClientError as e:
            return {
                "success": False,
                "error": f"请求失败: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"未知错误: {str(e)}"
            }
    
    async def scrape_webpage(self, url: str, selector: str = None,
                           extract_links: bool = False,
                           extract_images: bool = False,
                           extract_text: bool = True) -> Dict[str, Any]:
        """抓取网页内容"""
        try:
            # 发送HTTP请求
            response = await self.http_request(url)
            
            if not response["success"]:
                return response
            
            html_content = response["content"]
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            result = {
                "success": True,
                "url": url,
                "title": soup.title.string if soup.title else None,
                "status_code": response["status_code"]
            }
            
            # 提取指定选择器的内容
            if selector:
                elements = soup.select(selector)
                result["selected_elements"] = []
                
                for element in elements:
                    result["selected_elements"].append({
                        "tag": element.name,
                        "text": element.get_text(strip=True),
                        "attributes": dict(element.attrs),
                        "html": str(element)
                    })
            
            # 提取文本内容
            if extract_text:
                # 移除脚本和样式
                for script in soup(["script", "style"]):
                    script.decompose()
                
                result["text_content"] = soup.get_text(separator='\n', strip=True)
                result["word_count"] = len(result["text_content"].split())
            
            # 提取链接
            if extract_links:
                links = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    absolute_url = urljoin(url, href)
                    links.append({
                        "text": link.get_text(strip=True),
                        "href": href,
                        "absolute_url": absolute_url
                    })
                result["links"] = links
            
            # 提取图片
            if extract_images:
                images = []
                for img in soup.find_all('img', src=True):
                    src = img['src']
                    absolute_url = urljoin(url, src)
                    images.append({
                        "src": src,
                        "absolute_url": absolute_url,
                        "alt": img.get('alt', ''),
                        "title": img.get('title', '')
                    })
                result["images"] = images
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": f"网页抓取失败: {str(e)}"
            }
    
    async def download_file(self, url: str, save_path: str = None,
                           chunk_size: int = 8192) -> Dict[str, Any]:
        """下载文件"""
        try:
            if not self.session:
                await self.__aenter__()
            
            start_time = time.time()
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {response.reason}"
                    }
                
                # 获取文件名
                if not save_path:
                    content_disposition = response.headers.get('Content-Disposition')
                    if content_disposition:
                        filename_match = re.search(r'filename="([^"]+)"', content_disposition)
                        if filename_match:
                            save_path = filename_match.group(1)
                        else:
                            save_path = urlparse(url).path.split('/')[-1]
                    else:
                        save_path = urlparse(url).path.split('/')[-1]
                
                if not save_path:
                    save_path = 'downloaded_file'
                
                # 检查文件大小
                content_length = response.headers.get('Content-Length')
                if content_length and int(content_length) > self.max_response_size:
                    return {
                        "success": False,
                        "error": f"文件过大: {content_length} bytes"
                    }
                
                # 下载文件
                total_size = 0
                with open(save_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(chunk_size):
                        f.write(chunk)
                        total_size += len(chunk)
                        
                        # 检查大小限制
                        if total_size > self.max_response_size:
                            return {
                                "success": False,
                                "error": f"文件过大: {total_size} bytes"
                            }
                
                download_time = time.time() - start_time
                
                return {
                    "success": True,
                    "file_path": save_path,
                    "size": total_size,
                    "download_time": download_time,
                    "speed": total_size / download_time if download_time > 0 else 0
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"文件下载失败: {str(e)}"
            }
    
    async def check_url_status(self, urls: List[str]) -> Dict[str, Any]:
        """批量检查URL状态"""
        try:
            if not self.session:
                await self.__aenter__()
            
            results = []
            
            # 并发检查URL
            async def check_single_url(url: str) -> Dict[str, Any]:
                try:
                    start_time = time.time()
                    async with self.session.head(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        response_time = time.time() - start_time
                        return {
                            "url": url,
                            "status_code": response.status,
                            "status": "ok" if 200 <= response.status < 400 else "error",
                            "response_time": response_time,
                            "headers": dict(response.headers)
                        }
                except Exception as e:
                    return {
                        "url": url,
                        "status_code": None,
                        "status": "error",
                        "error": str(e),
                        "response_time": None
                    }
            
            # 限制并发数
            semaphore = asyncio.Semaphore(10)
            
            async def check_with_semaphore(url: str):
                async with semaphore:
                    return await check_single_url(url)
            
            tasks = [check_with_semaphore(url) for url in urls]
            results = await asyncio.gather(*tasks)
            
            # 统计结果
            ok_count = sum(1 for r in results if r["status"] == "ok")
            error_count = len(results) - ok_count
            
            return {
                "success": True,
                "results": results,
                "summary": {
                    "total": len(urls),
                    "ok": ok_count,
                    "error": error_count
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"URL状态检查失败: {str(e)}"
            }
    
    async def extract_emails(self, url: str) -> Dict[str, Any]:
        """从网页提取邮箱地址"""
        try:
            # 抓取网页内容
            response = await self.scrape_webpage(url, extract_text=True)
            
            if not response["success"]:
                return response
            
            text_content = response.get("text_content", "")
            
            # 邮箱正则表达式
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, text_content)
            
            # 去重并排序
            unique_emails = sorted(list(set(emails)))
            
            return {
                "success": True,
                "url": url,
                "emails": unique_emails,
                "count": len(unique_emails)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"邮箱提取失败: {str(e)}"
            }
    
    async def extract_phone_numbers(self, url: str) -> Dict[str, Any]:
        """从网页提取电话号码"""
        try:
            # 抓取网页内容
            response = await self.scrape_webpage(url, extract_text=True)
            
            if not response["success"]:
                return response
            
            text_content = response.get("text_content", "")
            
            # 电话号码正则表达式（支持多种格式）
            phone_patterns = [
                r'\b\d{3}-\d{3}-\d{4}\b',  # ************
                r'\b\d{3}\.\d{3}\.\d{4}\b',  # ************
                r'\b\(\d{3}\)\s*\d{3}-\d{4}\b',  # (*************
                r'\b\d{10}\b',  # 1234567890
                r'\b1-\d{3}-\d{3}-\d{4}\b',  # 1-************
                r'\b\+\d{1,3}-\d{3,4}-\d{3,4}-\d{4}\b'  # ******-456-7890
            ]
            
            phone_numbers = []
            for pattern in phone_patterns:
                matches = re.findall(pattern, text_content)
                phone_numbers.extend(matches)
            
            # 去重并排序
            unique_phones = sorted(list(set(phone_numbers)))
            
            return {
                "success": True,
                "url": url,
                "phone_numbers": unique_phones,
                "count": len(unique_phones)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"电话号码提取失败: {str(e)}"
            }
    
    def parse_url(self, url: str) -> Dict[str, Any]:
        """解析URL组件"""
        try:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            
            return {
                "success": True,
                "url": url,
                "scheme": parsed.scheme,
                "netloc": parsed.netloc,
                "hostname": parsed.hostname,
                "port": parsed.port,
                "path": parsed.path,
                "query": parsed.query,
                "fragment": parsed.fragment,
                "query_params": query_params
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"URL解析失败: {str(e)}"
            }
