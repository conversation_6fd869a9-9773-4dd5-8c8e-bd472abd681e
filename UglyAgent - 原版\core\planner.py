#!/usr/bin/env python3
"""
UglyAgent 任务规划器
提供智能任务分解、规划和执行管理功能
"""

import json
import time
import uuid
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import asyncio

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"      # 待执行
    RUNNING = "running"      # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    BLOCKED = "blocked"      # 被阻塞


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Task:
    """任务类"""
    id: str
    name: str
    description: str
    tool_name: str
    parameters: Dict[str, Any]
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    dependencies: List[str] = None  # 依赖的任务ID
    created_time: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0
    result: Any = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 超时时间(秒)
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.metadata is None:
            self.metadata = {}
        if self.created_time == 0.0:
            self.created_time = time.time()


@dataclass
class Plan:
    """执行计划"""
    id: str
    name: str
    description: str
    tasks: List[Task]
    created_time: float = 0.0
    start_time: float = 0.0
    end_time: float = 0.0
    status: TaskStatus = TaskStatus.PENDING
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.created_time == 0.0:
            self.created_time = time.time()


class TaskPlanner:
    """任务规划器"""
    
    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.plans: Dict[str, Plan] = {}
        self.running_tasks: Dict[str, Task] = {}
        self.task_queue: List[Task] = []
        self.completed_tasks: Dict[str, Task] = {}
        self.failed_tasks: Dict[str, Task] = {}
        
        # 任务执行器映射
        self.task_executors: Dict[str, Callable] = {}
        
        # 规划模板
        self.planning_templates = {
            "file_analysis": self._create_file_analysis_plan,
            "web_scraping": self._create_web_scraping_plan,
            "code_review": self._create_code_review_plan,
            "system_monitoring": self._create_system_monitoring_plan
        }
    
    def register_executor(self, tool_name: str, executor: Callable):
        """注册任务执行器"""
        self.task_executors[tool_name] = executor
        logger.info(f"已注册任务执行器: {tool_name}")
    
    def create_plan(self, goal: str, context: Dict[str, Any] = None) -> str:
        """创建执行计划"""
        try:
            plan_id = str(uuid.uuid4())
            
            # 分析目标，选择合适的规划模板
            plan_type = self._analyze_goal(goal, context or {})
            
            if plan_type in self.planning_templates:
                tasks = self.planning_templates[plan_type](goal, context or {})
            else:
                tasks = self._create_generic_plan(goal, context or {})
            
            plan = Plan(
                id=plan_id,
                name=f"Plan for: {goal}",
                description=goal,
                tasks=tasks
            )
            
            self.plans[plan_id] = plan
            
            logger.info(f"创建执行计划: {plan_id} ({len(tasks)} 个任务)")
            return plan_id
            
        except Exception as e:
            logger.error(f"创建执行计划失败: {e}")
            return ""
    
    def _analyze_goal(self, goal: str, context: Dict[str, Any]) -> str:
        """分析目标，确定计划类型"""
        goal_lower = goal.lower()
        
        if any(keyword in goal_lower for keyword in ["分析文件", "analyze file", "文件分析"]):
            return "file_analysis"
        elif any(keyword in goal_lower for keyword in ["爬取", "scrape", "抓取网页"]):
            return "web_scraping"
        elif any(keyword in goal_lower for keyword in ["代码审查", "code review", "检查代码"]):
            return "code_review"
        elif any(keyword in goal_lower for keyword in ["监控系统", "system monitor", "系统状态"]):
            return "system_monitoring"
        else:
            return "generic"
    
    def _create_file_analysis_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建文件分析计划"""
        tasks = []
        file_path = context.get("file_path", "")
        
        # 任务1: 读取文件
        task1 = Task(
            id=str(uuid.uuid4()),
            name="读取文件",
            description=f"读取文件内容: {file_path}",
            tool_name="read_file",
            parameters={"file_path": file_path},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 分析代码结构
        task2 = Task(
            id=str(uuid.uuid4()),
            name="分析代码结构",
            description=f"分析代码文件结构: {file_path}",
            tool_name="analyze_code",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)
        
        # 任务3: 检查语法
        task3 = Task(
            id=str(uuid.uuid4()),
            name="检查语法",
            description=f"检查代码语法: {file_path}",
            tool_name="check_syntax",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task3)
        
        return tasks
    
    def _create_web_scraping_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建网页抓取计划"""
        tasks = []
        url = context.get("url", "")
        
        # 任务1: 检查URL状态
        task1 = Task(
            id=str(uuid.uuid4()),
            name="检查URL状态",
            description=f"检查URL可访问性: {url}",
            tool_name="check_url_status",
            parameters={"urls": [url]},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 抓取网页内容
        task2 = Task(
            id=str(uuid.uuid4()),
            name="抓取网页内容",
            description=f"抓取网页内容: {url}",
            tool_name="scrape_webpage",
            parameters={
                "url": url,
                "extract_links": True,
                "extract_images": True,
                "extract_text": True
            },
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)
        
        return tasks
    
    def _create_code_review_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建代码审查计划"""
        tasks = []
        file_path = context.get("file_path", "")
        
        # 任务1: 读取文件
        task1 = Task(
            id=str(uuid.uuid4()),
            name="读取代码文件",
            description=f"读取代码文件: {file_path}",
            tool_name="read_file",
            parameters={"file_path": file_path},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 代码分析
        task2 = Task(
            id=str(uuid.uuid4()),
            name="代码分析",
            description=f"分析代码质量: {file_path}",
            tool_name="analyze_code",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task2)
        
        # 任务3: 语法检查
        task3 = Task(
            id=str(uuid.uuid4()),
            name="语法检查",
            description=f"检查代码语法: {file_path}",
            tool_name="check_syntax",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL
        )
        tasks.append(task3)
        
        # 任务4: 提取函数
        task4 = Task(
            id=str(uuid.uuid4()),
            name="提取函数",
            description=f"提取函数定义: {file_path}",
            tool_name="extract_functions",
            parameters={"file_path": file_path},
            dependencies=[task1.id],
            priority=TaskPriority.LOW
        )
        tasks.append(task4)
        
        return tasks
    
    def _create_system_monitoring_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建系统监控计划"""
        tasks = []
        duration = context.get("duration", 60)
        
        # 任务1: 获取系统信息
        task1 = Task(
            id=str(uuid.uuid4()),
            name="获取系统信息",
            description="获取基本系统信息",
            tool_name="get_system_info",
            parameters={"info_type": "all"},
            priority=TaskPriority.HIGH
        )
        tasks.append(task1)
        
        # 任务2: 监控系统资源
        task2 = Task(
            id=str(uuid.uuid4()),
            name="监控系统资源",
            description=f"监控系统资源使用情况 ({duration}秒)",
            tool_name="monitor_system",
            parameters={"duration": duration, "interval": 5},
            dependencies=[task1.id],
            priority=TaskPriority.NORMAL,
            timeout=duration + 30
        )
        tasks.append(task2)
        
        # 任务3: 检查磁盘空间
        task3 = Task(
            id=str(uuid.uuid4()),
            name="检查磁盘空间",
            description="检查磁盘空间使用情况",
            tool_name="check_disk_space",
            parameters={},
            priority=TaskPriority.LOW
        )
        tasks.append(task3)
        
        return tasks
    
    def _create_generic_plan(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """创建通用计划"""
        # 简单的通用计划，可以根据需要扩展
        task = Task(
            id=str(uuid.uuid4()),
            name="执行目标",
            description=goal,
            tool_name="ai_chat",  # 使用AI对话来处理通用目标
            parameters={"message": goal},
            priority=TaskPriority.NORMAL
        )
        
        return [task]
    
    async def execute_plan(self, plan_id: str) -> Dict[str, Any]:
        """执行计划"""
        try:
            if plan_id not in self.plans:
                return {
                    "success": False,
                    "error": f"计划不存在: {plan_id}"
                }
            
            plan = self.plans[plan_id]
            plan.status = TaskStatus.RUNNING
            plan.start_time = time.time()
            
            logger.info(f"开始执行计划: {plan_id}")
            
            # 将任务添加到队列
            for task in plan.tasks:
                self.task_queue.append(task)
            
            # 执行任务
            await self._execute_tasks()
            
            # 检查计划完成状态
            completed_tasks = [t for t in plan.tasks if t.status == TaskStatus.COMPLETED]
            failed_tasks = [t for t in plan.tasks if t.status == TaskStatus.FAILED]
            
            if len(completed_tasks) == len(plan.tasks):
                plan.status = TaskStatus.COMPLETED
            elif failed_tasks:
                plan.status = TaskStatus.FAILED
            
            plan.end_time = time.time()
            
            return {
                "success": True,
                "plan_id": plan_id,
                "status": plan.status.value,
                "completed_tasks": len(completed_tasks),
                "failed_tasks": len(failed_tasks),
                "total_tasks": len(plan.tasks),
                "execution_time": plan.end_time - plan.start_time
            }
            
        except Exception as e:
            logger.error(f"执行计划失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_tasks(self):
        """执行任务队列"""
        while self.task_queue or self.running_tasks:
            # 启动可执行的任务
            await self._start_ready_tasks()
            
            # 等待运行中的任务
            if self.running_tasks:
                await asyncio.sleep(0.1)
            
            # 检查超时任务
            await self._check_timeout_tasks()
    
    async def _start_ready_tasks(self):
        """启动准备就绪的任务"""
        if len(self.running_tasks) >= self.max_concurrent_tasks:
            return
        
        ready_tasks = []
        
        for task in self.task_queue[:]:
            if self._is_task_ready(task):
                ready_tasks.append(task)
                self.task_queue.remove(task)
                
                if len(ready_tasks) + len(self.running_tasks) >= self.max_concurrent_tasks:
                    break
        
        # 按优先级排序
        ready_tasks.sort(key=lambda t: t.priority.value, reverse=True)
        
        for task in ready_tasks:
            await self._start_task(task)
    
    def _is_task_ready(self, task: Task) -> bool:
        """检查任务是否准备就绪"""
        if task.status != TaskStatus.PENDING:
            return False
        
        # 检查依赖任务是否完成
        for dep_id in task.dependencies:
            if dep_id not in self.completed_tasks:
                return False
        
        return True
    
    async def _start_task(self, task: Task):
        """启动任务"""
        try:
            task.status = TaskStatus.RUNNING
            task.start_time = time.time()
            self.running_tasks[task.id] = task
            
            logger.info(f"启动任务: {task.name} ({task.id})")
            
            # 异步执行任务
            asyncio.create_task(self._execute_task(task))
            
        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            task.status = TaskStatus.FAILED
            task.error = str(e)
    
    async def _execute_task(self, task: Task):
        """执行单个任务"""
        try:
            executor = self.task_executors.get(task.tool_name)
            if not executor:
                raise ValueError(f"未找到任务执行器: {task.tool_name}")
            
            # 执行任务
            result = await executor(**task.parameters)
            
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.end_time = time.time()
            
            # 移动到完成列表
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            self.completed_tasks[task.id] = task
            
            logger.info(f"任务完成: {task.name} ({task.id})")
            
        except Exception as e:
            logger.error(f"任务执行失败: {task.name} - {e}")
            
            task.error = str(e)
            task.retry_count += 1
            
            if task.retry_count < task.max_retries:
                # 重试任务
                task.status = TaskStatus.PENDING
                self.task_queue.append(task)
                logger.info(f"任务重试: {task.name} (第{task.retry_count}次)")
            else:
                # 任务失败
                task.status = TaskStatus.FAILED
                task.end_time = time.time()
                self.failed_tasks[task.id] = task
            
            # 从运行列表移除
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
    
    async def _check_timeout_tasks(self):
        """检查超时任务"""
        current_time = time.time()
        
        for task_id, task in list(self.running_tasks.items()):
            if current_time - task.start_time > task.timeout:
                logger.warning(f"任务超时: {task.name} ({task.id})")
                
                task.status = TaskStatus.FAILED
                task.error = "任务执行超时"
                task.end_time = current_time
                
                del self.running_tasks[task_id]
                self.failed_tasks[task_id] = task
    
    def get_plan_status(self, plan_id: str) -> Dict[str, Any]:
        """获取计划状态"""
        if plan_id not in self.plans:
            return {
                "success": False,
                "error": f"计划不存在: {plan_id}"
            }
        
        plan = self.plans[plan_id]
        
        task_stats = {
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0,
            "blocked": 0
        }
        
        for task in plan.tasks:
            task_stats[task.status.value] += 1
        
        return {
            "success": True,
            "plan_id": plan_id,
            "plan_name": plan.name,
            "plan_status": plan.status.value,
            "task_stats": task_stats,
            "total_tasks": len(plan.tasks),
            "created_time": plan.created_time,
            "start_time": plan.start_time,
            "end_time": plan.end_time
        }
    
    def cancel_plan(self, plan_id: str) -> Dict[str, Any]:
        """取消计划"""
        try:
            if plan_id not in self.plans:
                return {
                    "success": False,
                    "error": f"计划不存在: {plan_id}"
                }
            
            plan = self.plans[plan_id]
            
            # 取消所有相关任务
            cancelled_count = 0
            for task in plan.tasks:
                if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                    task.status = TaskStatus.CANCELLED
                    task.end_time = time.time()
                    cancelled_count += 1
                    
                    # 从队列和运行列表中移除
                    if task in self.task_queue:
                        self.task_queue.remove(task)
                    if task.id in self.running_tasks:
                        del self.running_tasks[task.id]
            
            plan.status = TaskStatus.CANCELLED
            plan.end_time = time.time()
            
            logger.info(f"计划已取消: {plan_id} ({cancelled_count} 个任务)")
            
            return {
                "success": True,
                "plan_id": plan_id,
                "cancelled_tasks": cancelled_count
            }
            
        except Exception as e:
            logger.error(f"取消计划失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
